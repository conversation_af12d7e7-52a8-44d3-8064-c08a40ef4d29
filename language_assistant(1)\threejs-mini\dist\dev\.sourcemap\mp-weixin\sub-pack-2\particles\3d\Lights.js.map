{"version": 3, "file": "Lights.js", "sources": ["sub-pack-2/particles/3d/Lights.js"], "sourcesContent": ["import * as THREE from 'three';\r\n// import { ShadowMapViewer } from 'three/examples/jsm/utils/ShadowMapViewer.js';\r\n\r\nconst SHADOW_MAP_WIDTH = 4096;\r\nconst SHADOW_MAP_HEIGHT = 2048;\r\n\r\nexport class Lights extends THREE.Object3D {\r\n  constructor() {\r\n    super();\r\n    this.position.set(0, 500, 0);\r\n\r\n    const ambient = new THREE.AmbientLight(0x333333);\r\n    this.add(ambient);\r\n\r\n    const pointLight = (this.pointLight = new THREE.PointLight(0xffffff, 1, 700, 0));\r\n    pointLight.castShadow = true;\r\n    pointLight.shadow.camera.near = 10;\r\n    pointLight.shadow.camera.far = 700;\r\n    // pointLight.shadow.bias = 0.1;\r\n    pointLight.shadow.mapSize.width = SHADOW_MAP_WIDTH;\r\n    pointLight.shadow.mapSize.height = SHADOW_MAP_HEIGHT;\r\n    this.add(pointLight);\r\n\r\n    const directionalLight = new THREE.DirectionalLight(0xba8b8b, 0.5);\r\n    directionalLight.position.set(1, 1, 1);\r\n    this.add(directionalLight);\r\n\r\n    const directionalLight2 = new THREE.DirectionalLight(0x8bbab4, 0.3);\r\n    directionalLight2.position.set(1, 1, -1);\r\n    this.add(directionalLight2);\r\n\r\n    // const lightShadowMapViewer = this.lightShadowMapViewer = new ShadowMapViewer( pointLight );\r\n    // lightShadowMapViewer.position.x = 10;\r\n    // lightShadowMapViewer.position.y = window.innerHeight - ( SHADOW_MAP_HEIGHT / 6 ) - 10;\r\n    // lightShadowMapViewer.size.width = SHADOW_MAP_WIDTH / 6;\r\n    // lightShadowMapViewer.size.height = SHADOW_MAP_HEIGHT / 6;\r\n    // lightShadowMapViewer.update();\r\n  }\r\n\r\n  update(renderer) {\r\n    // this.pointLight.shadow.\r\n    // this.pointLight.shadowDarkness = this._shadowDarkness += (shadowDarkness - _shadowDarkness) * 0.1\r\n    // this.lightShadowMapViewer.render(renderer)\r\n  }\r\n}\r\n"], "names": ["THREE.Object3D", "THREE.AmbientLight", "THREE.PointLight", "THREE.DirectionalLight"], "mappings": ";;;AAGA,MAAM,mBAAmB;AACzB,MAAM,oBAAoB;AAEnB,MAAM,eAAeA,wCAAAA,GAAe;AAAA,EACzC,cAAc;AACZ;AACA,SAAK,SAAS,IAAI,GAAG,KAAK,CAAC;AAE3B,UAAM,UAAU,IAAIC,2CAAmB,OAAQ;AAC/C,SAAK,IAAI,OAAO;AAEhB,UAAM,aAAc,KAAK,aAAa,IAAIC,wCAAAA,GAAiB,UAAU,GAAG,KAAK,CAAC;AAC9E,eAAW,aAAa;AACxB,eAAW,OAAO,OAAO,OAAO;AAChC,eAAW,OAAO,OAAO,MAAM;AAE/B,eAAW,OAAO,QAAQ,QAAQ;AAClC,eAAW,OAAO,QAAQ,SAAS;AACnC,SAAK,IAAI,UAAU;AAEnB,UAAM,mBAAmB,IAAIC,wCAAAA,GAAuB,UAAU,GAAG;AACjE,qBAAiB,SAAS,IAAI,GAAG,GAAG,CAAC;AACrC,SAAK,IAAI,gBAAgB;AAEzB,UAAM,oBAAoB,IAAIA,wCAAAA,GAAuB,SAAU,GAAG;AAClE,sBAAkB,SAAS,IAAI,GAAG,GAAG,EAAE;AACvC,SAAK,IAAI,iBAAiB;AAAA,EAQ5B;AAAA,EAEA,OAAO,UAAU;AAAA,EAIjB;AACF;;"}