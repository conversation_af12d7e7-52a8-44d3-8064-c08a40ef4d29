<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D数字人模型测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: Arial, sans-serif;
        }
        
        .container {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            padding: 20px;
            text-align: center;
            background: rgba(255, 255, 255, 0.9);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            margin: 0;
            color: #006397;
            font-size: 24px;
        }
        
        .canvas-container {
            flex: 1;
            position: relative;
            overflow: hidden;
        }
        
        #threejs-canvas {
            width: 100%;
            height: 100%;
            display: block;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #006397;
            font-size: 18px;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e0e0e0;
            border-top: 4px solid #006397;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .controls {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .controls button {
            display: block;
            width: 100%;
            margin-bottom: 10px;
            padding: 8px 16px;
            background: #006397;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .controls button:hover {
            background: #004d73;
        }
        
        .controls button:last-child {
            margin-bottom: 0;
        }
        
        .info {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            font-size: 14px;
            color: #333;
            max-width: 300px;
        }
        
        .error {
            color: #ff4444;
            background: rgba(255, 68, 68, 0.1);
            border: 1px solid #ff4444;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
        
        .success {
            color: #00aa00;
            background: rgba(0, 170, 0, 0.1);
            border: 1px solid #00aa00;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>3D数字人模型测试</h1>
        </div>
        
        <div class="canvas-container">
            <canvas id="threejs-canvas"></canvas>
            
            <div class="loading" id="loading">
                <div class="loading-spinner"></div>
                <div>加载3D数字人模型中...</div>
            </div>
            
            <div class="controls">
                <button onclick="resetCamera()">重置视角</button>
                <button onclick="toggleAnimation()">切换动画</button>
                <button onclick="reloadModel()">重新加载</button>
            </div>
            
            <div class="info">
                <div><strong>操作说明：</strong></div>
                <div>• 鼠标拖拽：旋转模型</div>
                <div>• 滚轮：缩放</div>
                <div>• 右键拖拽：平移</div>
                <div id="model-info"></div>
                <div id="status"></div>
            </div>
        </div>
    </div>

    <!-- 引入 Three.js -->
    <script type="importmap">
    {
        "imports": {
            "three": "https://unpkg.com/three@0.172.0/build/three.module.js",
            "three/addons/": "https://unpkg.com/three@0.172.0/examples/jsm/"
        }
    }
    </script>

    <script type="module">
        import * as THREE from 'three';
        import { GLTFLoader } from 'three/addons/loaders/GLTFLoader.js';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';

        let scene, camera, renderer, model, mixer, clock, controls;
        let animationAction = null;
        let isAnimationPlaying = false;

        // 初始化
        function init() {
            console.log('开始初始化Three.js场景...');
            
            // 获取canvas元素
            const canvas = document.getElementById('threejs-canvas');
            const container = canvas.parentElement;
            
            // 创建场景
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0xf5f7fa);
            
            // 创建相机
            camera = new THREE.PerspectiveCamera(45, container.clientWidth / container.clientHeight, 1, 2000);
            camera.position.set(0, 100, 200);
            
            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ 
                canvas: canvas, 
                antialias: true,
                alpha: true 
            });
            renderer.setSize(container.clientWidth, container.clientHeight);
            renderer.setPixelRatio(window.devicePixelRatio);
            renderer.toneMapping = THREE.ACESFilmicToneMapping;
            renderer.toneMappingExposure = 1;
            
            // 添加光源
            const ambientLight = new THREE.AmbientLight(0xffffff, 0.8);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.6);
            directionalLight.position.set(5, 10, 5);
            scene.add(directionalLight);
            
            // 添加控制器
            controls = new OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;
            controls.minDistance = 50;
            controls.maxDistance = 500;
            controls.target.set(0, 50, 0);
            controls.update();
            
            // 创建时钟
            clock = new THREE.Clock();
            
            // 开始渲染循环
            animate();
            
            // 加载模型
            loadModel();
            
            // 监听窗口大小变化
            window.addEventListener('resize', onWindowResize);
            
            console.log('Three.js场景初始化完成');
        }

        // 加载GLB模型
        function loadModel() {
            const loader = new GLTFLoader();
            const modelPaths = [
                'http://minhandash.edu.izdax.cn/Teacher_001.glb',
                '/static/Teacher.glb'
            ];
            
            let currentPathIndex = 0;
            
            function tryLoadModel() {
                if (currentPathIndex >= modelPaths.length) {
                    showError('所有模型路径都加载失败');
                    return;
                }
                
                const modelPath = modelPaths[currentPathIndex];
                console.log(`尝试加载模型 (${currentPathIndex + 1}/${modelPaths.length}):`, modelPath);
                updateStatus(`正在加载模型: ${modelPath}`);
                
                loader.load(
                    modelPath,
                    function(gltf) {
                        console.log('模型加载成功!', gltf);
                        
                        // 移除旧模型
                        if (model) {
                            scene.remove(model);
                        }
                        
                        model = gltf.scene;
                        
                        // 调整模型大小和位置
                        model.scale.set(220, 220, 220);
                        model.position.set(0, -50, 0);
                        
                        // 添加到场景
                        scene.add(model);
                        
                        // 设置动画
                        if (gltf.animations && gltf.animations.length > 0) {
                            mixer = new THREE.AnimationMixer(model);
                            animationAction = mixer.clipAction(gltf.animations[0]);
                            animationAction.play();
                            isAnimationPlaying = true;
                            console.log(`找到 ${gltf.animations.length} 个动画`);
                        }
                        
                        // 隐藏加载界面
                        document.getElementById('loading').style.display = 'none';
                        
                        // 显示成功信息
                        showSuccess('3D数字人模型加载成功！');
                        updateModelInfo(gltf);
                        
                        console.log('Teacher 3D模型加载并显示成功!');
                    },
                    function(progress) {
                        if (progress.total > 0) {
                            const percent = (progress.loaded / progress.total * 100).toFixed(1);
                            console.log(`模型加载进度: ${percent}%`);
                            updateStatus(`加载进度: ${percent}%`);
                        }
                    },
                    function(error) {
                        console.error(`模型加载失败 (路径 ${currentPathIndex + 1}):`, error);
                        currentPathIndex++;
                        if (currentPathIndex < modelPaths.length) {
                            setTimeout(tryLoadModel, 1000);
                        } else {
                            document.getElementById('loading').style.display = 'none';
                            showError('所有模型路径都加载失败: ' + error.message);
                        }
                    }
                );
            }
            
            tryLoadModel();
        }

        // 动画循环
        function animate() {
            requestAnimationFrame(animate);
            
            // 更新控制器
            if (controls) {
                controls.update();
            }
            
            // 更新动画
            if (mixer && clock) {
                const delta = clock.getDelta();
                mixer.update(delta);
            }
            
            // 渲染场景
            if (renderer && scene && camera) {
                renderer.render(scene, camera);
            }
        }

        // 窗口大小变化处理
        function onWindowResize() {
            const container = document.getElementById('threejs-canvas').parentElement;
            camera.aspect = container.clientWidth / container.clientHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(container.clientWidth, container.clientHeight);
        }

        // 重置相机
        window.resetCamera = function() {
            camera.position.set(0, 100, 200);
            controls.target.set(0, 50, 0);
            controls.update();
        }

        // 切换动画
        window.toggleAnimation = function() {
            if (animationAction) {
                if (isAnimationPlaying) {
                    animationAction.pause();
                    isAnimationPlaying = false;
                    updateStatus('动画已暂停');
                } else {
                    animationAction.play();
                    isAnimationPlaying = true;
                    updateStatus('动画已播放');
                }
            } else {
                updateStatus('没有可用的动画');
            }
        }

        // 重新加载模型
        window.reloadModel = function() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('status').innerHTML = '';
            loadModel();
        }

        // 更新状态信息
        function updateStatus(message) {
            document.getElementById('status').innerHTML = `<div style="margin-top: 10px;"><strong>状态:</strong> ${message}</div>`;
        }

        // 显示错误信息
        function showError(message) {
            document.getElementById('status').innerHTML = `<div class="error"><strong>错误:</strong> ${message}</div>`;
        }

        // 显示成功信息
        function showSuccess(message) {
            document.getElementById('status').innerHTML = `<div class="success"><strong>成功:</strong> ${message}</div>`;
        }

        // 更新模型信息
        function updateModelInfo(gltf) {
            let info = '<div style="margin-top: 10px;"><strong>模型信息:</strong></div>';
            info += `<div>• 动画数量: ${gltf.animations ? gltf.animations.length : 0}</div>`;
            info += `<div>• 场景对象: ${gltf.scene.children.length}</div>`;
            document.getElementById('model-info').innerHTML = info;
        }

        // 页面加载完成后初始化
        window.addEventListener('load', init);
    </script>
</body>
</html>
