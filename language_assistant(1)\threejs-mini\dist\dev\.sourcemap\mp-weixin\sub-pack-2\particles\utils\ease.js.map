{"version": 3, "file": "ease.js", "sources": ["sub-pack-2/particles/utils/ease.js"], "sourcesContent": ["const basic = {\r\n  Linear: {\r\n    None: function (e) {\r\n      return e;\r\n    }\r\n  },\r\n  Quad: {\r\n    In: function (e) {\r\n      return e * e;\r\n    },\r\n    Out: function (e) {\r\n      return e * (2 - e);\r\n    },\r\n    InOut: function (e) {\r\n      if ((e *= 2) < 1) return 0.5 * e * e;\r\n      return -0.5 * (--e * (e - 2) - 1);\r\n    }\r\n  },\r\n  Cubic: {\r\n    In: function (e) {\r\n      return e * e * e;\r\n    },\r\n    Out: function (e) {\r\n      return --e * e * e + 1;\r\n    },\r\n    InOut: function (e) {\r\n      if ((e *= 2) < 1) return 0.5 * e * e * e;\r\n      return 0.5 * ((e -= 2) * e * e + 2);\r\n    }\r\n  },\r\n  Quart: {\r\n    In: function (e) {\r\n      return e * e * e * e;\r\n    },\r\n    Out: function (e) {\r\n      return 1 - --e * e * e * e;\r\n    },\r\n    InOut: function (e) {\r\n      if ((e *= 2) < 1) return 0.5 * e * e * e * e;\r\n      return -0.5 * ((e -= 2) * e * e * e - 2);\r\n    }\r\n  },\r\n  Quint: {\r\n    In: function (e) {\r\n      return e * e * e * e * e;\r\n    },\r\n    Out: function (e) {\r\n      return --e * e * e * e * e + 1;\r\n    },\r\n    InOut: function (e) {\r\n      if ((e *= 2) < 1) return 0.5 * e * e * e * e * e;\r\n      return 0.5 * ((e -= 2) * e * e * e * e + 2);\r\n    }\r\n  },\r\n  Sine: {\r\n    In: function (e) {\r\n      return 1 - Math.cos((e * Math.PI) / 2);\r\n    },\r\n    Out: function (e) {\r\n      return Math.sin((e * Math.PI) / 2);\r\n    },\r\n    InOut: function (e) {\r\n      return 0.5 * (1 - Math.cos(Math.PI * e));\r\n    }\r\n  },\r\n  Expo: {\r\n    In: function (e) {\r\n      return e === 0 ? 0 : Math.pow(1024, e - 1);\r\n    },\r\n    Out: function (e) {\r\n      return e === 1 ? 1 : 1 - Math.pow(2, -10 * e);\r\n    },\r\n    InOut: function (e) {\r\n      if (e === 0) return 0;\r\n      if (e === 1) return 1;\r\n      if ((e *= 2) < 1) return 0.5 * Math.pow(1024, e - 1);\r\n      return 0.5 * (-Math.pow(2, -10 * (e - 1)) + 2);\r\n    }\r\n  },\r\n  Circ: {\r\n    In: function (e) {\r\n      return 1 - Math.sqrt(1 - e * e);\r\n    },\r\n    Out: function (e) {\r\n      return Math.sqrt(1 - --e * e);\r\n    },\r\n    InOut: function (e) {\r\n      if ((e *= 2) < 1) return -0.5 * (Math.sqrt(1 - e * e) - 1);\r\n      return 0.5 * (Math.sqrt(1 - (e -= 2) * e) + 1);\r\n    }\r\n  },\r\n  Elastic: {\r\n    In: function (e) {\r\n      var t,\r\n        n = 0.1,\r\n        r = 0.4;\r\n      if (e === 0) return 0;\r\n      if (e === 1) return 1;\r\n      if (!n || n < 1) {\r\n        n = 1;\r\n        t = r / 4;\r\n      } else t = (r * Math.asin(1 / n)) / (2 * Math.PI);\r\n      return -(n * Math.pow(2, 10 * (e -= 1)) * Math.sin(((e - t) * 2 * Math.PI) / r));\r\n    },\r\n    Out: function (e) {\r\n      var t,\r\n        n = 0.1,\r\n        r = 0.4;\r\n      if (e === 0) return 0;\r\n      if (e === 1) return 1;\r\n      if (!n || n < 1) {\r\n        n = 1;\r\n        t = r / 4;\r\n      } else t = (r * Math.asin(1 / n)) / (2 * Math.PI);\r\n      return n * Math.pow(2, -10 * e) * Math.sin(((e - t) * 2 * Math.PI) / r) + 1;\r\n    },\r\n    InOut: function (e) {\r\n      var t,\r\n        n = 0.1,\r\n        r = 0.4;\r\n      if (e === 0) return 0;\r\n      if (e === 1) return 1;\r\n      if (!n || n < 1) {\r\n        n = 1;\r\n        t = r / 4;\r\n      } else {\r\n        t = (r * Math.asin(1 / n)) / (2 * Math.PI);\r\n      }\r\n      if ((e *= 2) < 1)\r\n        return -0.5 * n * Math.pow(2, 10 * (e -= 1)) * Math.sin(((e - t) * 2 * Math.PI) / r);\r\n      return n * Math.pow(2, -10 * (e -= 1)) * Math.sin(((e - t) * 2 * Math.PI) / r) * 0.5 + 1;\r\n    }\r\n  },\r\n  Back: {\r\n    In: function (e) {\r\n      var t = 1.70158;\r\n      return e * e * ((t + 1) * e - t);\r\n    },\r\n    Out: function (e) {\r\n      var t = 1.70158;\r\n      return --e * e * ((t + 1) * e + t) + 1;\r\n    },\r\n    InOut: function (e) {\r\n      var t = 1.70158 * 1.525;\r\n      if ((e *= 2) < 1) return 0.5 * e * e * ((t + 1) * e - t);\r\n      return 0.5 * ((e -= 2) * e * ((t + 1) * e + t) + 2);\r\n    }\r\n  },\r\n  Bounce: {\r\n    In: function (e) {\r\n      return 1 - basic.Bounce.Out(1 - e);\r\n    },\r\n    Out: function (e) {\r\n      if (e < 1 / 2.75) {\r\n        return 7.5625 * e * e;\r\n      } else if (e < 2 / 2.75) {\r\n        return 7.5625 * (e -= 1.5 / 2.75) * e + 0.75;\r\n      } else if (e < 2.5 / 2.75) {\r\n        return 7.5625 * (e -= 2.25 / 2.75) * e + 0.9375;\r\n      } else {\r\n        return 7.5625 * (e -= 2.625 / 2.75) * e + 0.984375;\r\n      }\r\n    },\r\n    InOut: function (e) {\r\n      if (e < 0.5) return basic.Bounce.In(e * 2) * 0.5;\r\n      return basic.Bounce.Out(e * 2 - 1) * 0.5 + 0.5;\r\n    }\r\n  }\r\n};\r\n\r\nexport { basic };\r\n"], "names": [], "mappings": ";AAAK,MAAC,QAAQ;AAAA,EAkBZ,OAAO;AAAA,IACL,IAAI,SAAU,GAAG;AACf,aAAO,IAAI,IAAI;AAAA,IACjB;AAAA,IACA,KAAK,SAAU,GAAG;AAChB,aAAO,EAAE,IAAI,IAAI,IAAI;AAAA,IACvB;AAAA,IACA,OAAO,SAAU,GAAG;AAClB,WAAK,KAAK,KAAK,EAAG,QAAO,MAAM,IAAI,IAAI;AACvC,aAAO,QAAQ,KAAK,KAAK,IAAI,IAAI;AAAA,IACnC;AAAA,EACJ;AA2IA;;"}