"use strict";
const common_vendor = require("../common/vendor.js");
const _sfc_main = {
  __name: "PlatformCanvas",
  props: {
    type: {
      type: String,
      default: "webgl"
    },
    canvasId: {
      type: String,
      required: true
    }
  },
  emits: ["useCanvas", "touchstart", "touchmove", "touchend", "touchcancel", "tap"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const handleTouch = (e) => {
      if (e.type === "click") {
        e = {
          ...e,
          type: "tap"
        };
      }
      emit(e.type, e);
    };
    const instance = common_vendor.getCurrentInstance();
    common_vendor.onMounted(async () => {
      await common_vendor.nextTick$1();
      setTimeout(() => {
        getCanvasNode();
      }, 100);
    });
    const getCanvasNode = () => {
      const query = common_vendor.index.createSelectorQuery().in(instance);
      query.select(`#${props.canvasId}`).node().exec((res) => {
        if (res && res[0] && res[0].node) {
          const canvas = res[0].node;
          console.log("Canvas节点获取成功:", canvas);
          const dpr = common_vendor.index.getSystemInfoSync().pixelRatio || 1;
          query.select(`#${props.canvasId}`).boundingClientRect().exec((rectRes) => {
            if (rectRes && rectRes[0]) {
              const { width, height } = rectRes[0];
              canvas.width = width * dpr;
              canvas.height = height * dpr;
              console.log("微信小程序Canvas尺寸设置:", {
                displayWidth: width,
                displayHeight: height,
                actualWidth: canvas.width,
                actualHeight: canvas.height,
                dpr,
                platform: "微信小程序"
              });
              const useFrame = (callback) => {
                const animate = () => {
                  callback();
                  requestAnimationFrame(animate);
                };
                animate();
              };
              const recomputeSize = () => {
                return new Promise((resolve) => {
                  query.select(`#${props.canvasId}`).boundingClientRect().exec((newRectRes) => {
                    if (newRectRes && newRectRes[0]) {
                      const { width: newWidth, height: newHeight } = newRectRes[0];
                      canvas.width = newWidth * dpr;
                      canvas.height = newHeight * dpr;
                      console.log("Canvas尺寸重新计算:", { width: newWidth, height: newHeight });
                      resolve({ width: newWidth, height: newHeight });
                    }
                  });
                });
              };
              emit("useCanvas", {
                canvas,
                useFrame,
                recomputeSize,
                width: canvas.width,
                height: canvas.height
              });
            } else {
              console.error("无法获取Canvas显示尺寸");
              const defaultWidth = 375;
              const defaultHeight = 250;
              canvas.width = defaultWidth * dpr;
              canvas.height = defaultHeight * dpr;
              emit("useCanvas", {
                canvas,
                useFrame: () => {
                },
                recomputeSize: () => Promise.resolve({ width: defaultWidth, height: defaultHeight }),
                width: canvas.width,
                height: canvas.height
              });
            }
          });
        } else {
          console.error("无法获取Canvas节点，重试中...");
          setTimeout(() => {
            getCanvasNode();
          }, 500);
        }
      });
    };
    return (_ctx, _cache) => {
      return {
        a: __props.type,
        b: __props.canvasId,
        c: __props.canvasId,
        d: common_vendor.o(handleTouch),
        e: common_vendor.o(handleTouch),
        f: common_vendor.o(handleTouch),
        g: common_vendor.o(handleTouch),
        h: common_vendor.o(handleTouch)
      };
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-24260282"]]);
wx.createComponent(Component);
