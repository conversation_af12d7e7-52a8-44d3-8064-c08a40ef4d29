<custom-navbar wx:if="{{a}}" class="data-v-d78077b6" u-i="d78077b6-0" bind:__l="__l" u-p="{{a}}"/><view class="page-details data-v-d78077b6"><view class="digital-human-3d-container data-v-d78077b6"><platform-canvas wx:if="{{b}}" binduseCanvas="{{c}}" bindtouchstart="{{d}}" bindtouchmove="{{e}}" bindtouchend="{{f}}" class="three-canvas data-v-d78077b6" u-i="d78077b6-1" bind:__l="__l" u-p="{{g}}"/><view wx:if="{{h}}" class="loading-overlay data-v-d78077b6"><view class="loading-spinner data-v-d78077b6"></view><text class="loading-text data-v-d78077b6">加载3D数字人中...</text></view><view wx:if="{{i}}" class="fallback-container data-v-d78077b6"><view class="teacher-avatar data-v-d78077b6"><view class="avatar-circle data-v-d78077b6"><text class="avatar-emoji data-v-d78077b6">👨‍🏫</text></view><view class="avatar-glow data-v-d78077b6"></view></view><view class="teacher-info data-v-d78077b6"><text class="teacher-name data-v-d78077b6">AI Teacher</text><text class="teacher-subtitle data-v-d78077b6">语言学习助手</text></view></view></view><view class="{{['content', 'data-v-d78077b6', U]}}"><view class="options-row data-v-d78077b6"><view class="option-item data-v-d78077b6"><image src="{{j}}" mode="aspectFit" class="icon data-v-d78077b6"></image><text class="label-text data-v-d78077b6">{{k}}</text></view><view class="option-item-location data-v-d78077b6"><text class="label data-v-d78077b6">{{l}}：</text><uni-data-select wx:if="{{n}}" class="select-box data-v-d78077b6" u-i="d78077b6-2" bind:__l="__l" bindupdateModelValue="{{m}}" u-p="{{n}}"></uni-data-select></view><uni-icons wx:if="{{o}}" class="arrow-right data-v-d78077b6" u-i="d78077b6-3" bind:__l="__l" u-p="{{o}}"></uni-icons><view class="option-item-location data-v-d78077b6"><text class="label data-v-d78077b6">{{p}}：</text><uni-data-select wx:if="{{r}}" class="select-box data-v-d78077b6" u-i="d78077b6-4" bind:__l="__l" bindupdateModelValue="{{q}}" u-p="{{r}}"></uni-data-select></view></view><view class="Plate data-v-d78077b6"><view class="Plate-nav data-v-d78077b6"><text class="Plate-Student data-v-d78077b6">学生家长 : </text><text class="Plate-name data-v-d78077b6"> 买买提</text></view><view class="Plate-nav data-v-d78077b6"><text class="Plate-Training data-v-d78077b6">{{s}} : </text><text class="Plate-name data-v-d78077b6">参加学生家长会</text></view></view><view class="focus-line data-v-d78077b6"></view><view class="avatar-section data-v-d78077b6"><view class="avatar-container data-v-d78077b6"><image src="{{t}}" class="avatar-image data-v-d78077b6"/><image src="{{v}}" class="zoom-icon data-v-d78077b6"/></view></view><view class="chat-container data-v-d78077b6"><scroll-view class="chat-messages data-v-d78077b6" scroll-y scroll-into-view="{{x}}" scroll-with-animation="{{true}}" bindscrolltoupper="{{y}}"><block wx:for="{{w}}" wx:for-item="msg" wx:key="w"><view wx:if="{{msg.a}}" class="bubble-row user-row data-v-d78077b6" id="{{msg.k}}"><image wx:if="{{msg.b}}" src="{{msg.c}}" class="avatar-user data-v-d78077b6"></image><view class="bubble-cn user-bubble data-v-d78077b6"><text wx:if="{{msg.d}}" class="bubble-cn-text data-v-d78077b6">{{msg.e}}</text><view wx:else class="voice-message data-v-d78077b6" bindtap="{{msg.j}}"><uni-icons wx:if="{{msg.g}}" class="data-v-d78077b6" u-i="{{msg.f}}" bind:__l="__l" u-p="{{msg.g}}"></uni-icons><text class="voice-text data-v-d78077b6">{{msg.h}}s</text><view wx:if="{{msg.i}}" class="unread-dot data-v-d78077b6"></view></view></view></view><view wx:elif="{{msg.l}}" class="bubble-row data-v-d78077b6" id="{{msg.v}}"><view class="message-icon-container data-v-d78077b6"><image src="{{msg.m}}" class="message-icon data-v-d78077b6"></image></view><view class="bubble-cn data-v-d78077b6"><text wx:if="{{msg.n}}" class="bubble-cn-text data-v-d78077b6">{{msg.o}}</text><view wx:else class="voice-message data-v-d78077b6" bindtap="{{msg.t}}"><uni-icons wx:if="{{msg.q}}" class="data-v-d78077b6" u-i="{{msg.p}}" bind:__l="__l" u-p="{{msg.q}}"></uni-icons><text class="voice-text data-v-d78077b6">{{msg.r}}s</text><view wx:if="{{msg.s}}" class="unread-dot data-v-d78077b6"></view></view></view></view></block></scroll-view></view><view class="bottom-bar data-v-d78077b6"><scroll-view class="category-buttons data-v-d78077b6" scroll-x="true" show-scrollbar="false"><view wx:for="{{z}}" wx:for-item="item" wx:key="b" class="{{['category-button', 'data-v-d78077b6', item.c && 'active']}}" bindtap="{{item.d}}">{{item.a}}</view></scroll-view><view class="input-wrapper data-v-d78077b6"><view class="mode-switch data-v-d78077b6" bindtap="{{B}}"><image src="{{A}}" class="mode-icon data-v-d78077b6" mode="aspectFit"></image></view><view wx:if="{{C}}" class="input-box data-v-d78077b6"><input type="text" placeholder="{{D}}" class="message-input data-v-d78077b6" value="{{E}}" bindinput="{{F}}"/></view><view wx:else class="voice-btn data-v-d78077b6" catchtouchstart="{{H}}" catchtouchend="{{I}}" catchtouchcancel="{{J}}" catchtouchmove="{{K}}"><text class="voice-btn-text data-v-d78077b6">{{G}}</text></view><button class="send-btn data-v-d78077b6" bindtap="{{M}}">{{L}}</button></view></view><view wx:if="{{N}}" class="recording-overlay data-v-d78077b6"><view class="{{['recording-box', 'data-v-d78077b6', T && 'cancel-recording']}}"><view class="recording-icon-wrapper data-v-d78077b6"><image wx:if="{{O}}" src="{{P}}" class="cancel-icon data-v-d78077b6"></image><view wx:else class="recording-waves data-v-d78077b6"><view wx:for="{{Q}}" wx:for-item="i" wx:key="a" class="wave data-v-d78077b6" style="{{'animation-delay:' + i.b}}"></view></view></view><text class="recording-status data-v-d78077b6">{{R}}</text><text class="recording-tip data-v-d78077b6">{{S}}</text></view></view></view></view>