"use strict";
require("../../chunks/three/build/three.module.min.js");
const subPack2_particles_core_settings = require("../core/settings.js");
const _mpChunkDeps_three_build_three_core_min = require("../../chunks/three/build/three.core.min.js");
var particles_default$1 = "uniform sampler2D texturePosition;\r\nvarying float vLife;\n\nvoid main() {\r\n\n    vec4 positionInfo = texture2D(texturePosition, position.xy);\r\n    vec4 worldPosition = modelMatrix * vec4(positionInfo.xyz, 1.0);\r\n    vec4 mvPosition = viewMatrix * worldPosition;\r\n\n    vLife = positionInfo.w;\n\n    gl_PointSize = 1300.0 / length(mvPosition.xyz) * smoothstep(0.0, 0.2, positionInfo.w);\r\n    gl_Position = projectionMatrix * mvPosition;\n\n}\n\n////	#include <project_vertex>\n\n////    #include <worldpos_vertex>";
var particles_default = "varying float vLife;\r\nuniform vec3 color1;\r\nuniform vec3 color2;\n\nvoid main() {\r\n\n    vec3 outgoingLight = mix(color2, color1, smoothstep(0.0, 0.7, vLife));\n\ngl_FragColor = vec4( outgoingLight, 1.0 );\n\n}\n\n////uniform vec3 diffuse;\n\n////    #include <opaque_fragment>";
var particlesDistance_default$1 = "uniform sampler2D texturePosition;\n\nvarying vec4 vWorldPosition;\n\nvoid main() {\n\n    vec4 positionInfo = texture2D( texturePosition, position.xy );\n\n    vec4 worldPosition = modelMatrix * vec4( positionInfo.xyz, 1.0 );\r\n    vec4 mvPosition = viewMatrix * worldPosition;\n\n    gl_PointSize = 50.0 / length( mvPosition.xyz );\n\n    vWorldPosition = worldPosition;\n\n    gl_Position = projectionMatrix * mvPosition;\n\n}";
var particlesDistance_default = "uniform vec3 lightPos;\r\nvarying vec4 vWorldPosition;\n\n#include <common>\n\nvec4 pack1K ( float depth ) {\n\n   depth /= 1000.0;\r\n   const vec4 bitSh = vec4( 256.0 * 256.0 * 256.0, 256.0 * 256.0, 256.0, 1.0 );\r\n   const vec4 bitMsk = vec4( 0.0, 1.0 / 256.0, 1.0 / 256.0, 1.0 / 256.0 );\r\n   vec4 res = fract( depth * bitSh );\r\n   res -= res.xxyz * bitMsk;\r\n   return res;\n\n}\n\nfloat unpack1K ( vec4 color ) {\n\n   const vec4 bitSh = vec4( 1.0 / ( 256.0 * 256.0 * 256.0 ), 1.0 / ( 256.0 * 256.0 ), 1.0 / 256.0, 1.0 );\r\n   return dot( color, bitSh ) * 1000.0;\n\n}\n\nvoid main () {\n\n   gl_FragColor = pack1K( length( vWorldPosition.xyz - lightPos.xyz ) );\n\n}";
const TEXTURE_WIDTH = subPack2_particles_core_settings.settings.simulatorTextureWidth;
const TEXTURE_HEIGHT = subPack2_particles_core_settings.settings.simulatorTextureHeight;
const AMOUNT = TEXTURE_WIDTH * TEXTURE_HEIGHT;
const undef = void 0;
class Particles extends _mpChunkDeps_three_build_three_core_min.Rr {
  constructor(renderer) {
    super();
    this._renderer = renderer;
    this._tmpColor = new _mpChunkDeps_three_build_three_core_min.$r();
    this._color1 = new _mpChunkDeps_three_build_three_core_min.$r(subPack2_particles_core_settings.settings.color1);
    this._color2 = new _mpChunkDeps_three_build_three_core_min.$r(subPack2_particles_core_settings.settings.color2);
    this._meshes = [
      // this._triangleMesh = this._createTriangleMesh(),
      this._particleMesh = this._createParticleMesh()
    ];
    this._particleMesh.visible = false;
    this.add(this._particleMesh);
  }
  _createParticleMesh() {
    const position = new Float32Array(AMOUNT * 3);
    let i3;
    for (let i = 0; i < AMOUNT; i++) {
      i3 = i * 3;
      position[i3 + 0] = i % TEXTURE_WIDTH / TEXTURE_WIDTH;
      position[i3 + 1] = ~~(i / TEXTURE_WIDTH) / TEXTURE_HEIGHT;
    }
    const geometry = new _mpChunkDeps_three_build_three_core_min.zn();
    geometry.setAttribute("position", new _mpChunkDeps_three_build_three_core_min.cn(position, 3));
    const material = new _mpChunkDeps_three_build_three_core_min.Jn({
      uniforms: _mpChunkDeps_three_build_three_core_min.qn.merge([
        // THREE.ShaderLib.standard.uniforms,
        {
          texturePosition: { type: "t", value: undef },
          color1: { type: "c", value: undef },
          color2: { type: "c", value: undef }
        }
      ]),
      vertexShader: particles_default$1,
      fragmentShader: particles_default,
      blending: _mpChunkDeps_three_build_three_core_min.m
      // lights: true
      // fog: true
    });
    material.uniforms.color1.value = this._color1;
    material.uniforms.color2.value = this._color2;
    const mesh = new _mpChunkDeps_three_build_three_core_min.Ua(geometry, material);
    mesh.customDistanceMaterial = new _mpChunkDeps_three_build_three_core_min.Jn({
      uniforms: {
        lightPos: { type: "v3", value: new _mpChunkDeps_three_build_three_core_min.Ii(0, 0, 0) },
        texturePosition: { type: "t", value: undef }
      },
      vertexShader: particlesDistance_default$1,
      fragmentShader: particlesDistance_default,
      depthTest: true,
      depthWrite: true,
      side: _mpChunkDeps_three_build_three_core_min.d,
      blending: _mpChunkDeps_three_build_three_core_min.m
    });
    mesh.castShadow = true;
    mesh.receiveShadow = true;
    return mesh;
  }
  // _createTriangleMesh() {
  //
  //   const position = new Float32Array(AMOUNT * 3 * 3);
  //   const positionFlip = new Float32Array(AMOUNT * 3 * 3);
  //   const fboUV = new Float32Array(AMOUNT * 2 * 3);
  //
  //   const PI = Math.PI;
  //   const angle = PI * 2 / 3;
  //   const angles = [
  //     Math.sin(angle * 2 + PI),
  //     Math.cos(angle * 2 + PI),
  //     Math.sin(angle + PI),
  //     Math.cos(angle + PI),
  //     Math.sin(angle * 3 + PI),
  //     Math.cos(angle * 3 + PI),
  //     Math.sin(angle * 2),
  //     Math.cos(angle * 2),
  //     Math.sin(angle),
  //     Math.cos(angle),
  //     Math.sin(angle * 3),
  //     Math.cos(angle * 3)
  //   ];
  //   let i6, i9;
  //   for (let i = 0; i < AMOUNT; i++) {
  //     i6 = i * 6;
  //     i9 = i * 9;
  //     if (i % 2) {
  //       position[i9 + 0] = angles[0];
  //       position[i9 + 1] = angles[1];
  //       position[i9 + 3] = angles[2];
  //       position[i9 + 4] = angles[3];
  //       position[i9 + 6] = angles[4];
  //       position[i9 + 7] = angles[5];
  //
  //       positionFlip[i9 + 0] = angles[6];
  //       positionFlip[i9 + 1] = angles[7];
  //       positionFlip[i9 + 3] = angles[8];
  //       positionFlip[i9 + 4] = angles[9];
  //       positionFlip[i9 + 6] = angles[10];
  //       positionFlip[i9 + 7] = angles[11];
  //     } else {
  //       positionFlip[i9 + 0] = angles[0];
  //       positionFlip[i9 + 1] = angles[1];
  //       positionFlip[i9 + 3] = angles[2];
  //       positionFlip[i9 + 4] = angles[3];
  //       positionFlip[i9 + 6] = angles[4];
  //       positionFlip[i9 + 7] = angles[5];
  //
  //       position[i9 + 0] = angles[6];
  //       position[i9 + 1] = angles[7];
  //       position[i9 + 3] = angles[8];
  //       position[i9 + 4] = angles[9];
  //       position[i9 + 6] = angles[10];
  //       position[i9 + 7] = angles[11];
  //     }
  //
  //     fboUV[i6 + 0] = fboUV[i6 + 2] = fboUV[i6 + 4] = (i % TEXTURE_WIDTH) / TEXTURE_WIDTH;
  //     fboUV[i6 + 1] = fboUV[i6 + 3] = fboUV[i6 + 5] = ~~(i / TEXTURE_WIDTH) / TEXTURE_HEIGHT;
  //   }
  //   const geometry = new THREE.BufferGeometry();
  //   geometry.setAttribute('position', new THREE.BufferAttribute(position, 3));
  //   geometry.setAttribute('positionFlip', new THREE.BufferAttribute(positionFlip, 3));
  //   geometry.setAttribute('fboUV', new THREE.BufferAttribute(fboUV, 2));
  //
  //   const material = new THREE.ShaderMaterial({
  //     uniforms: {
  //       texturePosition: { type: 't', value: undef },
  //       flipRatio: { type: 'f', value: 0 },
  //       color1: { type: 'c', value: undef },
  //       color2: { type: 'c', value: undef },
  //       cameraMatrix: { type: 'm4', value: undef }
  //     }
  //     ,
  //     vertexShader: VERT_TRIANGLES,
  //     fragmentShader: FRAG_PARTICLES,
  //     blending: THREE.NoBlending
  //   });
  //
  //   material.uniforms.color1.value = this._color1;
  //   material.uniforms.color2.value = this._color2;
  //   material.uniforms.cameraMatrix.value = settings.camera.matrixWorld;
  //
  //   const mesh = new THREE.Mesh(geometry, material);
  //
  //   mesh.customDistanceMaterial = new THREE.ShaderMaterial({
  //     uniforms: {
  //       lightPos: { type: 'v3', value: new THREE.Vector3(0, 0, 0) },
  //       texturePosition: { type: 't', value: undef },
  //       flipRatio: { type: 'f', value: 0 }
  //     },
  //     vertexShader: VERT_PARTICLES_DISTANCE,
  //     fragmentShader: FRAG_PARTICLES_DISTANCE,
  //     depthTest: true,
  //     depthWrite: true,
  //     side: THREE.BackSide,
  //     blending: THREE.NoBlending
  //   });
  //
  //   // mesh.motionMaterial = new MeshMotionMaterial({
  //   //   uniforms: {
  //   //     texturePosition: { type: 't', value: undef },
  //   //     texturePrevPosition: { type: 't', value: undef },
  //   //     flipRatio: { type: 'f', value: 0 }
  //   //   },
  //   //   vertexShader: shaderParse(VERT_TRIANGLES_MOTION),
  //   //   depthTest: true,
  //   //   depthWrite: true,
  //   //   side: THREE.DoubleSide,
  //   //   blending: THREE.NoBlending
  //   // });
  //
  //   mesh.castShadow = true;
  //   mesh.receiveShadow = true;
  //
  //   return mesh;
  // }
  update(dt, simulator) {
    let mesh;
    this._particleMesh.visible = subPack2_particles_core_settings.settings.useTriangleParticles;
    this._tmpColor.setStyle(subPack2_particles_core_settings.settings.color1);
    this._color1.lerp(this._tmpColor, 0.05);
    this._tmpColor.setStyle(subPack2_particles_core_settings.settings.color2);
    this._color2.lerp(this._tmpColor, 0.05);
    for (let i = 0; i < this._meshes.length; i++) {
      mesh = this._meshes[i];
      mesh.material.uniforms.texturePosition.value = simulator.positionRenderTarget.texture;
      mesh.customDistanceMaterial.uniforms.texturePosition.value = simulator.positionRenderTarget.texture;
      if (mesh.material.uniforms.flipRatio) {
        mesh.material.uniforms.flipRatio.value ^= 1;
        mesh.customDistanceMaterial.uniforms.flipRatio.value ^= 1;
      }
    }
  }
}
exports.Particles = Particles;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/sub-pack-2/particles/3d/Particles.js.map
