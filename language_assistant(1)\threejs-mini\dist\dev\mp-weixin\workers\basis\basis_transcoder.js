const __workerAdapter = require("../worker-adapter.js");
const MessageData = __workerAdapter.MessageData
self = __workerAdapter.proxySelf
const WebAssembly = __workerAdapter.WebAssembly
setWASMInstantiateInputMapper(() => 'wasms/basis_transcoder.wasm.br')
"use strict";var e,r,t=(r="undefined"!=typeof document?null===(e=document.currentScript)||void 0===e?void 0:e.src:void 0,"undefined"!=typeof __filename&&(r=r||__filename),function(){var e,t,n,a,o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=new Promise(((r,n)=>{e=r,t=n})),s="object"==typeof window,c="function"==typeof importScripts,u="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node&&"renderer"!=process.type,l=Object.assign({},o),d="";if(u){var p=require("fs"),f=require("path");d=__dirname+"/",a=e=>(e=I(e)?new URL(e):f.normalize(e),p.readFileSync(e)),n=function(e){var r=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return e=I(e)?new URL(e):f.normalize(e),new Promise(((t,n)=>{p.readFile(e,r?void 0:"utf8",((e,a)=>{e?n(e):t(r?a.buffer:a)}))}))},!o.thisProgram&&process.argv.length>1&&process.argv[1].replace(/\\/g,"/"),process.argv.slice(2)}else(s||c)&&(c?d=self.location.href:"undefined"!=typeof document&&document.currentScript&&(d=document.currentScript.src),r&&(d=r),d=d.startsWith("blob:")?"":d.substr(0,d.replace(/[?#].*/,"").lastIndexOf("/")+1),c&&(a=e=>{var r=new XMLHttpRequest;return r.open("GET",e,!1),r.responseType="arraybuffer",r.send(null),new Uint8Array(r.response)}),n=e=>I(e)?new Promise(((r,t)=>{var n=new XMLHttpRequest;n.open("GET",e,!0),n.responseType="arraybuffer",n.onload=()=>{200==n.status||0==n.status&&n.response?r(n.response):t(n.status)},n.onerror=t,n.send(null)})):fetch(e,{credentials:"same-origin"}).then((e=>e.ok?e.arrayBuffer():Promise.reject(new Error(e.status+" : "+e.url)))));var h=o.print||console.log.bind(console),v=o.printErr||console.error.bind(console);Object.assign(o,l),l=null;var m,y,g,T,$,w,b,C,P,A=o.wasmBinary,E=!1;function S(){var e=m.buffer;o.HEAP8=y=new Int8Array(e),o.HEAP16=T=new Int16Array(e),o.HEAPU8=g=new Uint8Array(e),o.HEAPU16=$=new Uint16Array(e),o.HEAP32=w=new Int32Array(e),o.HEAPU32=b=new Uint32Array(e),o.HEAPF32=C=new Float32Array(e),o.HEAPF64=P=new Float64Array(e)}var _=[],F=[],W=[];function O(e){_.unshift(e)}function j(e){W.unshift(e)}var U=0,R=null;function H(e){var r;null===(r=o.onAbort)||void 0===r||r.call(o,e),v(e="Aborted("+e+")"),E=!0,e+=". Build with -sASSERTIONS for more info.";var n=new WebAssembly.RuntimeError(e);throw t(n),n}var k,x=e=>e.startsWith("data:application/octet-stream;base64,"),I=e=>e.startsWith("file://");function D(){var e,r="basis_transcoder.wasm";return x(r)?r:(e=r,o.locateFile?o.locateFile(e,d):d+e)}function V(e){if(e==k&&A)return new Uint8Array(A);if(a)return a(e);throw"both async and sync fetching of the wasm failed"}function L(e,r,t){return function(e){return A?Promise.resolve().then((()=>V(e))):n(e).then((e=>new Uint8Array(e)),(()=>V(e)))}(e).then((e=>WebAssembly.instantiate(e,r))).then(t,(e=>{v("failed to asynchronously prepare wasm: ".concat(e)),H(e)}))}var B=e=>{e.forEach((e=>e(o)))};class ExceptionInfo{constructor(e){this.excPtr=e,this.ptr=e-24}set_type(e){b[this.ptr+4>>2]=e}get_type(){return b[this.ptr+4>>2]}set_destructor(e){b[this.ptr+8>>2]=e}get_destructor(){return b[this.ptr+8>>2]}set_caught(e){e=e?1:0,y[this.ptr+12]=e}get_caught(){return 0!=y[this.ptr+12]}set_rethrown(e){e=e?1:0,y[this.ptr+13]=e}get_rethrown(){return 0!=y[this.ptr+13]}init(e,r){this.set_adjusted_ptr(0),this.set_type(e),this.set_destructor(r)}set_adjusted_ptr(e){b[this.ptr+16>>2]=e}get_adjusted_ptr(){return b[this.ptr+16>>2]}}var M={},z=e=>{for(;e.length;){var r=e.pop();e.pop()(r)}};function N(e){return this.fromWireType(b[e>>2])}var X,K,G,q={},J={},Q={},Z=e=>{throw new X(e)},Y=(e,r,t)=>{function n(r){var n=t(r);n.length!==e.length&&Z("Mismatched type converter count");for(var a=0;a<e.length;++a)te(e[a],n[a])}e.forEach((e=>Q[e]=r));var a=new Array(r.length),o=[],i=0;r.forEach(((e,r)=>{J.hasOwnProperty(e)?a[r]=J[e]:(o.push(e),q.hasOwnProperty(e)||(q[e]=[]),q[e].push((()=>{a[r]=J[e],++i===o.length&&n(a)})))})),0===o.length&&n(a)},ee=e=>{for(var r="",t=e;g[t];)r+=K[g[t++]];return r},re=e=>{throw new G(e)};function te(e,r){return function(e,r){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=r.name;if(e||re('type "'.concat(n,'" must have a positive integer typeid pointer')),J.hasOwnProperty(e)){if(t.ignoreDuplicateRegistrations)return;re("Cannot register type '".concat(n,"' twice"))}if(J[e]=r,delete Q[e],q.hasOwnProperty(e)){var a=q[e];delete q[e],a.forEach((e=>e()))}}(e,r,arguments.length>2&&void 0!==arguments[2]?arguments[2]:{})}var ne=8,ae=e=>{re(e.$$.ptrType.registeredClass.name+" instance already deleted")},oe=!1,ie=e=>{},se=e=>{e.count.value-=1,0===e.count.value&&(e=>{e.smartPtr?e.smartPtrType.rawDestructor(e.smartPtr):e.ptrType.registeredClass.rawDestructor(e.ptr)})(e)},ce=(e,r,t)=>{if(r===t)return e;if(void 0===t.baseClass)return null;var n=ce(e,r,t.baseClass);return null===n?null:t.downcast(n)},ue={},le={},de=(e,r)=>(r=((e,r)=>{for(void 0===r&&re("ptr should not be undefined");e.baseClass;)r=e.upcast(r),e=e.baseClass;return r})(e,r),le[r]),pe=(e,r)=>(r.ptrType&&r.ptr||Z("makeClassHandle requires ptr and ptrType"),!!r.smartPtrType!=!!r.smartPtr&&Z("Both smartPtrType and smartPtr must be specified"),r.count={value:1},he(Object.create(e,{$$:{value:r,writable:!0}})));function fe(e){var r=this.getPointee(e);if(!r)return this.destructor(e),null;var t=de(this.registeredClass,r);if(void 0!==t){if(0===t.$$.count.value)return t.$$.ptr=r,t.$$.smartPtr=e,t.clone();var n=t.clone();return this.destructor(e),n}function a(){return this.isSmartPointer?pe(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:r,smartPtrType:this,smartPtr:e}):pe(this.registeredClass.instancePrototype,{ptrType:this,ptr:e})}var o,i=this.registeredClass.getActualType(r),s=ue[i];if(!s)return a.call(this);o=this.isConst?s.constPointerType:s.pointerType;var c=ce(r,this.registeredClass,o.registeredClass);return null===c?a.call(this):this.isSmartPointer?pe(o.registeredClass.instancePrototype,{ptrType:o,ptr:c,smartPtrType:this,smartPtr:e}):pe(o.registeredClass.instancePrototype,{ptrType:o,ptr:c})}var he=e=>"undefined"==typeof FinalizationRegistry?(he=e=>e,e):(oe=new FinalizationRegistry((e=>{se(e.$$)})),he=e=>{var r=e.$$;if(r.smartPtr){var t={$$:r};oe.register(e,t,e)}return e},ie=e=>oe.unregister(e),he(e));function ve(){}var me=(e,r)=>Object.defineProperty(r,"name",{value:e}),ye=(e,r,t)=>{if(void 0===e[r].overloadTable){var n=e[r];e[r]=function(){for(var n=arguments.length,a=new Array(n),o=0;o<n;o++)a[o]=arguments[o];return e[r].overloadTable.hasOwnProperty(a.length)||re("Function '".concat(t,"' called with an invalid number of arguments (").concat(a.length,") - expects one of (").concat(e[r].overloadTable,")!")),e[r].overloadTable[a.length].apply(this,a)},e[r].overloadTable=[],e[r].overloadTable[n.argCount]=n}},ge=(e,r,t)=>{o.hasOwnProperty(e)?((void 0===t||void 0!==o[e].overloadTable&&void 0!==o[e].overloadTable[t])&&re("Cannot register public name '".concat(e,"' twice")),ye(o,e,e),o.hasOwnProperty(t)&&re("Cannot register multiple overloads of a function with the same number of arguments (".concat(t,")!")),o[e].overloadTable[t]=r):(o[e]=r,void 0!==t&&(o[e].numArguments=t))};function Te(e,r,t,n,a,o,i,s){this.name=e,this.constructor=r,this.instancePrototype=t,this.rawDestructor=n,this.baseClass=a,this.getActualType=o,this.upcast=i,this.downcast=s,this.pureVirtualFunctions=[]}var $e=(e,r,t)=>{for(;r!==t;)r.upcast||re("Expected null or instance of ".concat(t.name,", got an instance of ").concat(r.name)),e=r.upcast(e),r=r.baseClass;return e};function we(e,r){if(null===r)return this.isReference&&re("null is not a valid ".concat(this.name)),0;r.$$||re('Cannot pass "'.concat(Ge(r),'" as a ').concat(this.name)),r.$$.ptr||re("Cannot pass deleted object as a pointer of type ".concat(this.name));var t=r.$$.ptrType.registeredClass;return $e(r.$$.ptr,t,this.registeredClass)}function be(e,r){var t;if(null===r)return this.isReference&&re("null is not a valid ".concat(this.name)),this.isSmartPointer?(t=this.rawConstructor(),null!==e&&e.push(this.rawDestructor,t),t):0;r&&r.$$||re('Cannot pass "'.concat(Ge(r),'" as a ').concat(this.name)),r.$$.ptr||re("Cannot pass deleted object as a pointer of type ".concat(this.name)),!this.isConst&&r.$$.ptrType.isConst&&re("Cannot convert argument of type ".concat(r.$$.smartPtrType?r.$$.smartPtrType.name:r.$$.ptrType.name," to parameter type ").concat(this.name));var n=r.$$.ptrType.registeredClass;if(t=$e(r.$$.ptr,n,this.registeredClass),this.isSmartPointer)switch(void 0===r.$$.smartPtr&&re("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:r.$$.smartPtrType===this?t=r.$$.smartPtr:re("Cannot convert argument of type ".concat(r.$$.smartPtrType?r.$$.smartPtrType.name:r.$$.ptrType.name," to parameter type ").concat(this.name));break;case 1:t=r.$$.smartPtr;break;case 2:if(r.$$.smartPtrType===this)t=r.$$.smartPtr;else{var a=r.clone();t=this.rawShare(t,ze.toHandle((()=>a.delete()))),null!==e&&e.push(this.rawDestructor,t)}break;default:re("Unsupporting sharing policy")}return t}function Ce(e,r){if(null===r)return this.isReference&&re("null is not a valid ".concat(this.name)),0;r.$$||re('Cannot pass "'.concat(Ge(r),'" as a ').concat(this.name)),r.$$.ptr||re("Cannot pass deleted object as a pointer of type ".concat(this.name)),r.$$.ptrType.isConst&&re("Cannot convert argument of type ".concat(r.$$.ptrType.name," to parameter type ").concat(this.name));var t=r.$$.ptrType.registeredClass;return $e(r.$$.ptr,t,this.registeredClass)}function Pe(e,r,t,n,a,o,i,s,c,u,l){this.name=e,this.registeredClass=r,this.isReference=t,this.isConst=n,this.isSmartPointer=a,this.pointeeType=o,this.sharingPolicy=i,this.rawGetPointee=s,this.rawConstructor=c,this.rawShare=u,this.rawDestructor=l,a||void 0!==r.baseClass?this.toWireType=be:n?(this.toWireType=we,this.destructorFunction=null):(this.toWireType=Ce,this.destructorFunction=null)}var Ae,Ee,Se=(e,r,t)=>{o.hasOwnProperty(e)||Z("Replacing nonexistent public symbol"),void 0!==o[e].overloadTable&&void 0!==t?o[e].overloadTable[t]=r:(o[e]=r,o[e].argCount=t)},_e=[],Fe=e=>{var r=_e[e];return r||(e>=_e.length&&(_e.length=e+1),_e[e]=r=Ae.get(e)),r},We=function(e,r){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return e.includes("j")?((e,r,t)=>(e=e.replace(/p/g,"i"),(0,o["dynCall_"+e])(r,...t)))(e,r,t):Fe(r)(...t)},Oe=(e,r)=>{var t,n,a=(e=ee(e)).includes("j")?(t=e,n=r,function(){for(var e=arguments.length,r=new Array(e),a=0;a<e;a++)r[a]=arguments[a];return We(t,n,r)}):Fe(r);return"function"!=typeof a&&re("unknown function pointer with signature ".concat(e,": ").concat(r)),a},je=e=>{var r=gr(e),t=ee(r);return $r(r),t},Ue=(e,r)=>{var t=[],n={};throw r.forEach((function e(r){n[r]||J[r]||(Q[r]?Q[r].forEach(e):(t.push(r),n[r]=!0))})),new Ee("".concat(e,": ")+t.map(je).join([", "]))},Re=(e,r)=>{for(var t=[],n=0;n<e;n++)t.push(b[r+4*n>>2]);return t};function He(e,r,t,n,a,o){var i=r.length;i<2&&re("argTypes array size mismatch! Must at least get return value and 'this' types!");var s=null!==r[1]&&null!==t,c=function(e){for(var r=1;r<e.length;++r)if(null!==e[r]&&void 0===e[r].destructorFunction)return!0;return!1}(r),u="void"!==r[0].name,l=i-2,d=new Array(l),p=[],f=[];return me(e,(function(){var e;f.length=0,p.length=s?2:1,p[0]=a,s&&(e=r[1].toWireType(f,this),p[1]=e);for(var t=0;t<l;++t)d[t]=r[t+2].toWireType(f,t<0||arguments.length<=t?void 0:arguments[t]),p.push(d[t]);return function(t){if(c)z(f);else for(var n=s?1:2;n<r.length;n++){var a=1===n?e:d[n-2];null!==r[n].destructorFunction&&r[n].destructorFunction(a)}if(u)return r[0].fromWireType(t)}(n(...p))}))}var ke,xe,Ie,De=e=>{var r=(e=e.trim()).indexOf("(");return-1!==r?e.substr(0,r):e},Ve=[],Le=[],Be=e=>{e>9&&0==--Le[e+1]&&(Le[e]=void 0,Ve.push(e))},Me=()=>Le.length/2-5-Ve.length,ze={toValue:e=>(e||re("Cannot use deleted val. handle = "+e),Le[e]),toHandle:e=>{switch(e){case void 0:return 2;case null:return 4;case!0:return 6;case!1:return 8;default:var r=Ve.pop()||Le.length;return Le[r]=e,Le[r+1]=1,r}}},Ne={name:"emscripten::val",fromWireType:e=>{var r=ze.toValue(e);return Be(e),r},toWireType:(e,r)=>ze.toHandle(r),argPackAdvance:ne,readValueFromPointer:N,destructorFunction:null},Xe=(e,r,t)=>{switch(r){case 1:return t?function(e){return this.fromWireType(y[e])}:function(e){return this.fromWireType(g[e])};case 2:return t?function(e){return this.fromWireType(T[e>>1])}:function(e){return this.fromWireType($[e>>1])};case 4:return t?function(e){return this.fromWireType(w[e>>2])}:function(e){return this.fromWireType(b[e>>2])};default:throw new TypeError("invalid integer width (".concat(r,"): ").concat(e))}},Ke=(e,r)=>{var t=J[e];return void 0===t&&re("".concat(r," has unknown type ").concat(je(e))),t},Ge=e=>{if(null===e)return"null";var r=typeof e;return"object"===r||"array"===r||"function"===r?e.toString():""+e},qe=(e,r)=>{switch(r){case 4:return function(e){return this.fromWireType(C[e>>2])};case 8:return function(e){return this.fromWireType(P[e>>3])};default:throw new TypeError("invalid float width (".concat(r,"): ").concat(e))}},Je=(e,r,t)=>{switch(r){case 1:return t?e=>y[e]:e=>g[e];case 2:return t?e=>T[e>>1]:e=>$[e>>1];case 4:return t?e=>w[e>>2]:e=>b[e>>2];default:throw new TypeError("invalid integer width (".concat(r,"): ").concat(e))}},Qe="undefined"!=typeof TextDecoder?new TextDecoder:void 0,Ze=function(e){for(var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,t=r+(arguments.length>2&&void 0!==arguments[2]?arguments[2]:NaN),n=r;e[n]&&!(n>=t);)++n;if(n-r>16&&e.buffer&&Qe)return Qe.decode(e.subarray(r,n));for(var a="";r<n;){var o=e[r++];if(128&o){var i=63&e[r++];if(192!=(224&o)){var s=63&e[r++];if((o=224==(240&o)?(15&o)<<12|i<<6|s:(7&o)<<18|i<<12|s<<6|63&e[r++])<65536)a+=String.fromCharCode(o);else{var c=o-65536;a+=String.fromCharCode(55296|c>>10,56320|1023&c)}}else a+=String.fromCharCode((31&o)<<6|i)}else a+=String.fromCharCode(o)}return a},Ye="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0,er=(e,r)=>{for(var t=e,n=t>>1,a=n+r/2;!(n>=a)&&$[n];)++n;if((t=n<<1)-e>32&&Ye)return Ye.decode(g.subarray(e,t));for(var o="",i=0;!(i>=r/2);++i){var s=T[e+2*i>>1];if(0==s)break;o+=String.fromCharCode(s)}return o},rr=(e,r,t)=>{if(null!=t||(t=2147483647),t<2)return 0;for(var n=r,a=(t-=2)<2*e.length?t/2:e.length,o=0;o<a;++o){var i=e.charCodeAt(o);T[r>>1]=i,r+=2}return T[r>>1]=0,r-n},tr=e=>2*e.length,nr=(e,r)=>{for(var t=0,n="";!(t>=r/4);){var a=w[e+4*t>>2];if(0==a)break;if(++t,a>=65536){var o=a-65536;n+=String.fromCharCode(55296|o>>10,56320|1023&o)}else n+=String.fromCharCode(a)}return n},ar=(e,r,t)=>{if(null!=t||(t=2147483647),t<4)return 0;for(var n=r,a=n+t-4,o=0;o<e.length;++o){var i=e.charCodeAt(o);if(i>=55296&&i<=57343&&(i=65536+((1023&i)<<10)|1023&e.charCodeAt(++o)),w[r>>2]=i,(r+=4)+4>a)break}return w[r>>2]=0,r-n},or=e=>{for(var r=0,t=0;t<e.length;++t){var n=e.charCodeAt(t);n>=55296&&n<=57343&&++t,r+=4}return r},ir=(e,r,t)=>{var n=[],a=e.toWireType(n,t);return n.length&&(b[r>>2]=ze.toHandle(n)),a},sr=[],cr={},ur=e=>{var r=cr[e];return void 0===r?ee(e):r},lr=()=>{if("object"==typeof globalThis)return globalThis;function e(e){e.$$$embind_global$$$=e;var r="object"==typeof $$$embind_global$$$&&e.$$$embind_global$$$==e;return r||delete e.$$$embind_global$$$,r}if("object"==typeof $$$embind_global$$$)return $$$embind_global$$$;if("object"==typeof global&&e(global)?$$$embind_global$$$=global:"object"==typeof self&&e(self)&&($$$embind_global$$$=self),"object"==typeof $$$embind_global$$$)return $$$embind_global$$$;throw Error("unable to get global object.")},dr=Reflect.construct,pr=e=>{var r=(e-m.buffer.byteLength+65535)/65536|0;try{return m.grow(r),S(),1}catch(e){}},fr=[null,[],[]];X=o.InternalError=class InternalError extends Error{constructor(e){super(e),this.name="InternalError"}},(()=>{for(var e=new Array(256),r=0;r<256;++r)e[r]=String.fromCharCode(r);K=e})(),G=o.BindingError=class BindingError extends Error{constructor(e){super(e),this.name="BindingError"}},Object.assign(ve.prototype,{isAliasOf(e){if(!(this instanceof ve))return!1;if(!(e instanceof ve))return!1;var r=this.$$.ptrType.registeredClass,t=this.$$.ptr;e.$$=e.$$;for(var n=e.$$.ptrType.registeredClass,a=e.$$.ptr;r.baseClass;)t=r.upcast(t),r=r.baseClass;for(;n.baseClass;)a=n.upcast(a),n=n.baseClass;return r===n&&t===a},clone(){if(this.$$.ptr||ae(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var e,r=he(Object.create(Object.getPrototypeOf(this),{$$:{value:(e=this.$$,{count:e.count,deleteScheduled:e.deleteScheduled,preservePointerOnDelete:e.preservePointerOnDelete,ptr:e.ptr,ptrType:e.ptrType,smartPtr:e.smartPtr,smartPtrType:e.smartPtrType})}}));return r.$$.count.value+=1,r.$$.deleteScheduled=!1,r},delete(){this.$$.ptr||ae(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&re("Object already scheduled for deletion"),ie(this),se(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)},isDeleted(){return!this.$$.ptr},deleteLater(){return this.$$.ptr||ae(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&re("Object already scheduled for deletion"),this.$$.deleteScheduled=!0,this}}),Object.assign(Pe.prototype,{getPointee(e){return this.rawGetPointee&&(e=this.rawGetPointee(e)),e},destructor(e){var r;null===(r=this.rawDestructor)||void 0===r||r.call(this,e)},argPackAdvance:ne,readValueFromPointer:N,fromWireType:fe}),Ee=o.UnboundTypeError=(ke=Error,(Ie=me(xe="UnboundTypeError",(function(e){this.name=xe,this.message=e;var r=new Error(e).stack;void 0!==r&&(this.stack=this.toString()+"\n"+r.replace(/^Error(:[^\n]*)?\n/,""))}))).prototype=Object.create(ke.prototype),Ie.prototype.constructor=Ie,Ie.prototype.toString=function(){return void 0===this.message?this.name:"".concat(this.name,": ").concat(this.message)},Ie),Le.push(0,1,void 0,1,null,1,!0,1,!1,1),o.count_emval_handles=Me;var hr,vr,mr={K:(e,r,t)=>{throw new ExceptionInfo(e).init(r,t),e},G:()=>{H("")},s:e=>{var r=M[e];delete M[e];var t=r.rawConstructor,n=r.rawDestructor,a=r.fields,o=a.map((e=>e.getterReturnType)).concat(a.map((e=>e.setterArgumentType)));Y([e],o,(e=>{var o={};return a.forEach(((r,t)=>{var n=r.fieldName,i=e[t],s=r.getter,c=r.getterContext,u=e[t+a.length],l=r.setter,d=r.setterContext;o[n]={read:e=>i.fromWireType(s(c,e)),write:(e,r)=>{var t=[];l(d,e,u.toWireType(t,r)),z(t)}}})),[{name:r.name,fromWireType:e=>{var r={};for(var t in o)r[t]=o[t].read(e);return n(e),r},toWireType:(e,r)=>{for(var a in o)if(!(a in r))throw new TypeError('Missing field: "'.concat(a,'"'));var i=t();for(a in o)o[a].write(i,r[a]);return null!==e&&e.push(n,i),i},argPackAdvance:ne,readValueFromPointer:N,destructorFunction:n}]}))},C:(e,r,t,n,a)=>{},I:(e,r,t,n)=>{te(e,{name:r=ee(r),fromWireType:function(e){return!!e},toWireType:function(e,r){return r?t:n},argPackAdvance:ne,readValueFromPointer:function(e){return this.fromWireType(g[e])},destructorFunction:null})},w:(e,r,t,n,a,o,i,s,c,u,l,d,p)=>{l=ee(l),o=Oe(a,o),s&&(s=Oe(i,s)),u&&(u=Oe(c,u)),p=Oe(d,p);var f=(e=>{var r=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return r>=48&&r<=57?"_".concat(e):e})(l);ge(f,(function(){Ue("Cannot construct ".concat(l," due to unbound types"),[n])})),Y([e,r,t],n?[n]:[],(r=>{var t,a;r=r[0],a=n?(t=r.registeredClass).instancePrototype:ve.prototype;var i=me(l,(function(){if(Object.getPrototypeOf(this)!==c)throw new G("Use 'new' to construct "+l);if(void 0===v.constructor_body)throw new G(l+" has no accessible constructor");for(var e=arguments.length,r=new Array(e),t=0;t<e;t++)r[t]=arguments[t];var n=v.constructor_body[r.length];if(void 0===n)throw new G("Tried to invoke ctor of ".concat(l," with invalid number of parameters (").concat(r.length,") - expected (").concat(Object.keys(v.constructor_body).toString(),") parameters instead!"));return n.apply(this,r)})),c=Object.create(a,{constructor:{value:i}});i.prototype=c;var d,h,v=new Te(l,i,c,p,t,o,s,u);v.baseClass&&(null!==(h=(d=v.baseClass).__derivedClasses)&&void 0!==h||(d.__derivedClasses=[]),v.baseClass.__derivedClasses.push(v));var m=new Pe(l,v,!0,!1,!1),y=new Pe(l+"*",v,!1,!1,!1),g=new Pe(l+" const*",v,!1,!0,!1);return ue[e]={pointerType:y,constPointerType:g},Se(f,i),[m,y,g]}))},v:(e,r,t,n,a,o)=>{var i=Re(r,t);a=Oe(n,a),Y([],[e],(e=>{e=e[0];var t="constructor ".concat(e.name);if(void 0===e.registeredClass.constructor_body&&(e.registeredClass.constructor_body=[]),void 0!==e.registeredClass.constructor_body[r-1])throw new G("Cannot register multiple constructors with identical number of parameters (".concat(r-1,") for class '").concat(e.name,"'! Overload resolution is currently only performed using the parameter count, not actual type info!"));return e.registeredClass.constructor_body[r-1]=()=>{Ue("Cannot construct ".concat(e.name," due to unbound types"),i)},Y([],i,(n=>(n.splice(1,0,null),e.registeredClass.constructor_body[r-1]=He(t,n,null,a,o),[]))),[]}))},d:(e,r,t,n,a,o,i,s,c,u)=>{var l=Re(t,n);r=ee(r),r=De(r),o=Oe(a,o),Y([],[e],(e=>{e=e[0];var n="".concat(e.name,".").concat(r);function a(){Ue("Cannot call ".concat(n," due to unbound types"),l)}r.startsWith("@@")&&(r=Symbol[r.substring(2)]),s&&e.registeredClass.pureVirtualFunctions.push(r);var c=e.registeredClass.instancePrototype,u=c[r];return void 0===u||void 0===u.overloadTable&&u.className!==e.name&&u.argCount===t-2?(a.argCount=t-2,a.className=e.name,c[r]=a):(ye(c,r,n),c[r].overloadTable[t-2]=a),Y([],l,(a=>{var s=He(n,a,e,o,i);return void 0===c[r].overloadTable?(s.argCount=t-2,c[r]=s):c[r].overloadTable[t-2]=s,[]})),[]}))},m:(e,r,t)=>{e=ee(e),Y([],[r],(r=>(r=r[0],o[e]=r.fromWireType(t),[])))},H:e=>te(e,Ne),o:(e,r,t,n)=>{function a(){}r=ee(r),a.values={},te(e,{name:r,constructor:a,fromWireType:function(e){return this.constructor.values[e]},toWireType:(e,r)=>r.value,argPackAdvance:ne,readValueFromPointer:Xe(r,t,n),destructorFunction:null}),ge(r,a)},a:(e,r,t)=>{var n=Ke(e,"enum");r=ee(r);var a=n.constructor,o=Object.create(n.constructor.prototype,{value:{value:t},constructor:{value:me("".concat(n.name,"_").concat(r),(function(){}))}});a.values[t]=o,a[r]=o},A:(e,r,t)=>{te(e,{name:r=ee(r),fromWireType:e=>e,toWireType:(e,r)=>r,argPackAdvance:ne,readValueFromPointer:qe(r,t),destructorFunction:null})},i:(e,r,t,n,a,o,i,s)=>{var c=Re(r,t);e=ee(e),e=De(e),a=Oe(n,a),ge(e,(function(){Ue("Cannot call ".concat(e," due to unbound types"),c)}),r-1),Y([],c,(t=>{var n=[t[0],null].concat(t.slice(1));return Se(e,He(e,n,null,a,o),r-1),[]}))},l:(e,r,t,n,a)=>{r=ee(r);var o=e=>e;if(0===n){var i=32-8*t;o=e=>e<<i>>>i}var s=r.includes("unsigned");te(e,{name:r,fromWireType:o,toWireType:s?function(e,r){return r>>>0}:function(e,r){return r},argPackAdvance:ne,readValueFromPointer:Je(r,t,0!==n),destructorFunction:null})},f:(e,r,t)=>{var n=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][r];function a(e){var r=b[e>>2],t=b[e+4>>2];return new n(y.buffer,t,r)}te(e,{name:t=ee(t),fromWireType:a,argPackAdvance:ne,readValueFromPointer:a},{ignoreDuplicateRegistrations:!0})},z:(e,r)=>{var t="std::string"===(r=ee(r));te(e,{name:r,fromWireType(e){var r,n,a,o=b[e>>2],i=e+4;if(t)for(var s=i,c=0;c<=o;++c){var u=i+c;if(c==o||0==g[u]){var l=(a=u-s,(n=s)?Ze(g,n,a):"");void 0===r?r=l:(r+=String.fromCharCode(0),r+=l),s=u+1}}else{var d=new Array(o);for(c=0;c<o;++c)d[c]=String.fromCharCode(g[i+c]);r=d.join("")}return $r(e),r},toWireType(e,r){var n;r instanceof ArrayBuffer&&(r=new Uint8Array(r));var a="string"==typeof r;a||r instanceof Uint8Array||r instanceof Uint8ClampedArray||r instanceof Int8Array||re("Cannot pass non-string to std::string"),n=t&&a?(e=>{for(var r=0,t=0;t<e.length;++t){var n=e.charCodeAt(t);n<=127?r++:n<=2047?r+=2:n>=55296&&n<=57343?(r+=4,++t):r+=3}return r})(r):r.length;var o=Tr(4+n+1),i=o+4;if(b[o>>2]=n,t&&a)((e,r,t,n)=>{if(!(n>0))return 0;for(var a=t+n-1,o=0;o<e.length;++o){var i=e.charCodeAt(o);if(i>=55296&&i<=57343&&(i=65536+((1023&i)<<10)|1023&e.charCodeAt(++o)),i<=127){if(t>=a)break;r[t++]=i}else if(i<=2047){if(t+1>=a)break;r[t++]=192|i>>6,r[t++]=128|63&i}else if(i<=65535){if(t+2>=a)break;r[t++]=224|i>>12,r[t++]=128|i>>6&63,r[t++]=128|63&i}else{if(t+3>=a)break;r[t++]=240|i>>18,r[t++]=128|i>>12&63,r[t++]=128|i>>6&63,r[t++]=128|63&i}}r[t]=0})(r,g,i,n+1);else if(a)for(var s=0;s<n;++s){var c=r.charCodeAt(s);c>255&&($r(i),re("String has UTF-16 code units that do not fit in 8 bits")),g[i+s]=c}else for(s=0;s<n;++s)g[i+s]=r[s];return null!==e&&e.push($r,o),o},argPackAdvance:ne,readValueFromPointer:N,destructorFunction(e){$r(e)}})},u:(e,r,t)=>{var n,a,o,i;t=ee(t),2===r?(n=er,a=rr,i=tr,o=e=>$[e>>1]):4===r&&(n=nr,a=ar,i=or,o=e=>b[e>>2]),te(e,{name:t,fromWireType:e=>{for(var t,a=b[e>>2],i=e+4,s=0;s<=a;++s){var c=e+4+s*r;if(s==a||0==o(c)){var u=n(i,c-i);void 0===t?t=u:(t+=String.fromCharCode(0),t+=u),i=c+r}}return $r(e),t},toWireType:(e,n)=>{"string"!=typeof n&&re("Cannot pass non-string to C++ string type ".concat(t));var o=i(n),s=Tr(4+o+r);return b[s>>2]=o/r,a(n,s+4,o+r),null!==e&&e.push($r,s),s},argPackAdvance:ne,readValueFromPointer:N,destructorFunction(e){$r(e)}})},t:(e,r,t,n,a,o)=>{M[e]={name:ee(r),rawConstructor:Oe(t,n),rawDestructor:Oe(a,o),fields:[]}},c:(e,r,t,n,a,o,i,s,c,u)=>{M[e].fields.push({fieldName:ee(r),getterReturnType:t,getter:Oe(n,a),getterContext:o,setterArgumentType:i,setter:Oe(s,c),setterContext:u})},J:(e,r)=>{te(e,{isVoid:!0,name:r=ee(r),argPackAdvance:0,fromWireType:()=>{},toWireType:(e,r)=>{}})},F:(e,r,t)=>g.copyWithin(e,r,r+t),n:(e,r,t)=>(e=ze.toValue(e),r=Ke(r,"emval::as"),ir(r,t,e)),q:(e,r,t,n)=>(e=sr[e])(null,r=ze.toValue(r),t,n),p:(e,r,t,n,a)=>(e=sr[e])(r=ze.toValue(r),r[t=ur(t)],n,a),b:Be,x:e=>0===e?ze.toHandle(lr()):(e=ur(e),ze.toHandle(lr()[e])),j:(e,r,t)=>{var n=((e,r)=>{for(var t=new Array(e),n=0;n<e;++n)t[n]=Ke(b[r+4*n>>2],"parameter "+n);return t})(e,r),a=n.shift();e--;var o,i,s=new Array(e),c="methodCaller<(".concat(n.map((e=>e.name)).join(", "),") => ").concat(a.name,">");return o=me(c,((r,o,i,c)=>{for(var u=0,l=0;l<e;++l)s[l]=n[l].readValueFromPointer(c+u),u+=n[l].argPackAdvance;var d=1===t?dr(o,s):o.apply(r,s);return ir(a,i,d)})),i=sr.length,sr.push(o),i},r:e=>(e=ur(e),ze.toHandle(o[e])),g:(e,r)=>(e=ze.toValue(e),r=ze.toValue(r),ze.toHandle(e[r])),k:e=>{e>9&&(Le[e+1]+=1)},h:e=>ze.toHandle(ur(e)),e:e=>{var r=ze.toValue(e);z(r),Be(e)},D:e=>{var r,t,n=g.length,a=2147483648;if((e>>>=0)>a)return!1;for(var o=1;o<=4;o*=2){var i=n*(1+.2/o);i=Math.min(i,e+100663296);var s=Math.min(a,(r=Math.max(e,i),t=65536,Math.ceil(r/t)*t));if(pr(s))return!0}return!1},E:e=>52,B:function(e,r,t,n,a){return 70},y:(e,r,t,n)=>{for(var a,o,i,s=0,c=0;c<t;c++){var u=b[r>>2],l=b[r+4>>2];r+=8;for(var d=0;d<l;d++)a=e,o=g[u+d],i=void 0,i=fr[a],0===o||10===o?((1===a?h:v)(Ze(i)),i.length=0):i.push(o);s+=l}return b[n>>2]=s,0}},yr=function(){var e,r,n,a,i,s={a:mr};function c(e,r){var t;return yr=e.exports,m=yr.L,S(),Ae=yr.P,t=yr.M,F.unshift(t),function(){var e;if(U--,null===(e=o.monitorRunDependencies)||void 0===e||e.call(o,U),0==U&&R){var r=R;R=null,r()}}(),yr}if(U++,null===(e=o.monitorRunDependencies)||void 0===e||e.call(o,U),o.instantiateWasm)try{return o.instantiateWasm(s,c)}catch(e){v("Module.instantiateWasm callback failed with error: ".concat(e)),t(e)}return null!=k||(k=D()),(r=A,n=k,a=s,i=function(e){c(e.instance)},r||"function"!=typeof WebAssembly.instantiateStreaming||x(n)||I(n)||u||"function"!=typeof fetch?L(n,a,i):fetch(n,{credentials:"same-origin"}).then((e=>WebAssembly.instantiateStreaming(e,a).then(i,(function(e){return v("wasm streaming compile failed: ".concat(e)),v("falling back to ArrayBuffer instantiation"),L(n,a,i)}))))).catch(t),{}}(),gr=e=>(gr=yr.N)(e),Tr=e=>(Tr=yr.O)(e),$r=e=>($r=yr.Q)(e);function wr(){var r;function t(){var r,t;hr||(hr=1,o.calledRun=1,E||(B(F),e(o),null===(r=o.onRuntimeInitialized)||void 0===r||r.call(o),(t=o.postRun)&&("function"==typeof t&&(t=[t]),t.forEach(j)),B(W)))}U>0||!vr&&(vr=1,(r=o.preRun)&&("function"==typeof r&&(r=[r]),r.forEach(O)),B(_),U>0)||(o.setStatus?(o.setStatus("Running..."),setTimeout((()=>{setTimeout((()=>o.setStatus("")),1),t()}),1)):t())}if(o.dynCall_jiji=(e,r,t,n,a)=>(o.dynCall_jiji=yr.R)(e,r,t,n,a),R=function e(){hr||wr(),hr||(R=e)},o.preInit)for("function"==typeof o.preInit&&(o.preInit=[o.preInit]);o.preInit.length>0;)o.preInit.pop()();return wr(),i});
/**
 * @license
 * Copyright 2010-2025 Three.js Authors
 * SPDX-License-Identifier: MIT
 */var n,a,o;"undefined"!=typeof __THREE_DEVTOOLS__&&__THREE_DEVTOOLS__.dispatchEvent(new CustomEvent("register",{detail:{revision:"174"}})),"undefined"!=typeof window&&(window.__THREE__?console.warn("WARNING: Multiple instances of Three.js being imported."):window.__THREE__="174");var i=1023,s=37808,c=36492,u=37496,l=35842,d=33779,p=36196,f=37492,h=35840,v=33777,m=0,y=1,g=2,T=3,$=7,w=8,b=9,C=10,P=13,A={};self.addEventListener("message",(function(e){var r,s=e.data;switch(s.type){case"init":n=s.config,r=s.transcoderBinary,a=new Promise((e=>{t(o={wasmBinary:r,onRuntimeInitialized:e})})).then((()=>{o.initializeBasis(),void 0===o.KTX2File&&console.warn("THREE.KTX2Loader: Please update Basis Universal transcoder.")}));break;case"transcode":a.then((()=>{try{var{faces:e,buffers:r,width:t,height:a,hasAlpha:c,format:u,dfdFlags:l}=function(e){var r=new o.KTX2File(new Uint8Array(e));function t(){r.close(),r.delete()}if(!r.isValid())throw t(),new Error("THREE.KTX2Loader:\tInvalid or unsupported .ktx2 file");var a=r.isUASTC()?A.UASTC_4x4:A.ETC1S,s=r.getWidth(),c=r.getHeight(),u=r.getLayers()||1,l=r.getLevels(),d=r.getFaces(),p=r.getHasAlpha(),f=r.getDFDFlags(),{transcoderFormat:h,engineFormat:v}=function(e,r,t,a){for(var o,s,c=e===A.ETC1S?S:_,u=0;u<c.length;u++){var l=c[u];if(n[l.if]&&(l.basisFormat.includes(e)&&!(a&&l.transcoderFormat.length<2)&&(!l.needsPowerOfTwo||F(r)&&F(t))))return{transcoderFormat:o=l.transcoderFormat[a?1:0],engineFormat:s=l.engineFormat[a?1:0]}}return console.warn("THREE.KTX2Loader: No suitable compressed texture format found. Decoding to RGBA32."),o=P,s=i,{transcoderFormat:o,engineFormat:s}}(a,s,c,p);if(!s||!c||!l)throw t(),new Error("THREE.KTX2Loader:\tInvalid texture");if(!r.startTranscoding())throw t(),new Error("THREE.KTX2Loader: .startTranscoding failed");for(var m=[],y=[],g=0;g<d;g++){for(var T=[],$=0;$<l;$++){for(var w=[],b=void 0,C=void 0,E=0;E<u;E++){var O=r.getImageLevelInfo($,E,g);0!==g||0!==$||0!==E||O.origWidth%4==0&&O.origHeight%4==0||console.warn("THREE.KTX2Loader: ETC1S and UASTC textures should use multiple-of-four dimensions."),l>1?(b=O.origWidth,C=O.origHeight):(b=O.width,C=O.height);var j=new Uint8Array(r.getImageTranscodedSizeInBytes($,E,0,h));if(!r.transcodeImage(j,$,E,g,h,0,-1,-1))throw t(),new Error("THREE.KTX2Loader: .transcodeImage failed.");w.push(j)}var U=W(w);T.push({data:U,width:b,height:C}),y.push(U.buffer)}m.push({mipmaps:T,width:s,height:c,format:v})}return t(),{faces:m,buffers:y,width:s,height:c,hasAlpha:p,format:v,dfdFlags:f}}(s.buffer);self.postMessage({type:"transcode",id:s.id,faces:e,width:t,height:a,hasAlpha:c,format:u,dfdFlags:l},r)}catch(e){console.error(e),self.postMessage({type:"error",id:s.id,error:e.message})}}))}}));var E=[{if:"astcSupported",basisFormat:[A.UASTC_4x4],transcoderFormat:[C,C],engineFormat:[s,s],priorityETC1S:1/0,priorityUASTC:1,needsPowerOfTwo:!1},{if:"bptcSupported",basisFormat:[A.ETC1S,A.UASTC_4x4],transcoderFormat:[$,$],engineFormat:[c,c],priorityETC1S:3,priorityUASTC:2,needsPowerOfTwo:!1},{if:"dxtSupported",basisFormat:[A.ETC1S,A.UASTC_4x4],transcoderFormat:[g,T],engineFormat:[v,d],priorityETC1S:4,priorityUASTC:5,needsPowerOfTwo:!1},{if:"etc2Supported",basisFormat:[A.ETC1S,A.UASTC_4x4],transcoderFormat:[m,y],engineFormat:[f,u],priorityETC1S:1,priorityUASTC:3,needsPowerOfTwo:!1},{if:"etc1Supported",basisFormat:[A.ETC1S,A.UASTC_4x4],transcoderFormat:[m],engineFormat:[p],priorityETC1S:2,priorityUASTC:4,needsPowerOfTwo:!1},{if:"pvrtcSupported",basisFormat:[A.ETC1S,A.UASTC_4x4],transcoderFormat:[w,b],engineFormat:[h,l],priorityETC1S:5,priorityUASTC:6,needsPowerOfTwo:!0}],S=E.sort((function(e,r){return e.priorityETC1S-r.priorityETC1S})),_=E.sort((function(e,r){return e.priorityUASTC-r.priorityUASTC}));function F(e){return e<=2||!(e&e-1)&&0!==e}function W(e){if(1===e.length)return e[0];for(var r=0,t=0;t<e.length;t++){r+=e[t].byteLength}for(var n=new Uint8Array(r),a=0,o=0;o<e.length;o++){var i=e[o];n.set(i,a),a+=i.byteLength}return n}
