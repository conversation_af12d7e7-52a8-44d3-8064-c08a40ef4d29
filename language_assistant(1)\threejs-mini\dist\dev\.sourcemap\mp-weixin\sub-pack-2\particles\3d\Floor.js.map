{"version": 3, "file": "Floor.js", "sources": ["sub-pack-2/particles/3d/Floor.js"], "sourcesContent": ["import * as THREE from 'three';\r\nexport class Floor extends THREE.Mesh {\r\n  constructor() {\r\n    const geometry = new THREE.PlaneGeometry(4000, 4000, 10, 10);\r\n    const planeMaterial = new THREE.MeshStandardMaterial({\r\n      roughness: 0.7,\r\n      metalness: 1.0,\r\n      color: 0x333333,\r\n      emissive: 0x000000\r\n    });\r\n    super(geometry, planeMaterial);\r\n\r\n    this.rotation.x = -1.57;\r\n    this.receiveShadow = true;\r\n    this.position.y = -100;\r\n  }\r\n}\r\n"], "names": ["THREE.Mesh", "THREE.PlaneGeometry", "THREE.MeshStandardMaterial"], "mappings": ";;;AACO,MAAM,cAAcA,wCAAAA,GAAW;AAAA,EACpC,cAAc;AACZ,UAAM,WAAW,IAAIC,2CAAoB,KAAM,KAAM,IAAI,EAAE;AAC3D,UAAM,gBAAgB,IAAIC,2CAA2B;AAAA,MACnD,WAAW;AAAA,MACX,WAAW;AAAA,MACX,OAAO;AAAA,MACP,UAAU;AAAA,IAChB,CAAK;AACD,UAAM,UAAU,aAAa;AAE7B,SAAK,SAAS,IAAI;AAClB,SAAK,gBAAgB;AACrB,SAAK,SAAS,IAAI;AAAA,EACpB;AACF;;"}