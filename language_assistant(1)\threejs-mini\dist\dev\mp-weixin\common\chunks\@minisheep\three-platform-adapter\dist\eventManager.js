"use strict";
const _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm = require("../../../@dcloudio/uni-mp-weixin/dist/uni.api.esm.js");
const _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter = require("./Adapter.js");
const _mpChunkDeps__minisheep_miniProgramPolyfillCore_dist_wechat = require("../../mini-program-polyfill-core/dist/wechat.js");
function h(n, r, a, o) {
  const s = Object.assign({ top: 0, left: 0, width: 0, height: 0, right: 0, bottom: 0 }, r || {});
  let i = s.width + "px", c = s.height + "px", l = "block";
  const u = Object.defineProperties({}, { width: { get: () => i, set: (e) => {
    (e = "" + e).endsWith("px") && (i = e);
  } }, height: { get: () => c, set: (e) => {
    (e = "" + e).endsWith("px") && (c = e);
  } }, display: { get: () => l, set: (e) => {
    l = e;
  } } }), h2 = Object.getPrototypeOf(n), d2 = Object.defineProperties(_mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.E(Object.create(h2)), Object.assign({ style: { value: u, writable: false }, clientTop: { get: () => s.top }, clientLeft: { get: () => s.left }, clientWidth: { get: () => s.width }, clientHeight: { get: () => s.height }, offsetTop: { get: () => s.top }, offsetLeft: { get: () => s.left }, offsetWidth: { get: () => s.width }, offsetHeight: { get: () => s.height }, getBoundingClientRect: { value: () => s }, setAttribute: { value: _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.g }, removeAttribute: { value: _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.g }, ownerDocument: { value: a }, getRootNode: { value: a ? () => a : void 0 }, setPointerCapture: { value: _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.g }, releasePointerCapture: { value: _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.g }, requestPointerLock: { value: _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.g }, exitPointerLock: { value: _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.g }, focus: { value: _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.g } }, (null == o ? void 0 : o(u, h2)) || {}));
  return Object.setPrototypeOf(n, d2), f.add(n), { element: n, updateRectInfo(e) {
    Object.assign(s, e), i = e.width + "px", c = e.height + "px";
  } };
}
const f = /* @__PURE__ */ new WeakSet();
class d {
  static [Symbol.hasInstance](e) {
    return _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.p(this, e, f);
  }
  constructor() {
    throw new TypeError("Illegal constructor");
  }
}
function g(e) {
  switch (typeof e) {
    case "object":
      if ("Object" === e.constructor.name) {
        if (2 === Object.keys(e).length && "__typedArray" in e && "buffer" in e) return new globalThis[e.__typedArray](e.buffer);
        Object.entries(e).forEach(([t, n]) => {
          e[t] = g(n);
        });
      } else e instanceof Array && e.forEach((t, n) => {
        e[n] = g(t);
      });
      return e;
    case "string":
      if (e.startsWith("__bigint")) {
        const t = e.slice(9);
        return BigInt(t);
      }
      if (e.startsWith("__symbol")) {
        const t = e.slice(9);
        return Symbol.for(t) || Symbol(t);
      }
      return e.startsWith("__arraybuffer") ? new ArrayBuffer(0) : e;
    default:
      return e;
  }
}
function p(e) {
  switch (typeof e) {
    case "bigint":
      return `__bigint:${e.toString()}`;
    case "object":
      if (null === e) return null;
      if (e instanceof ArrayBuffer) return 0 === e.byteLength ? "__arraybuffer:0" : e;
      if (ArrayBuffer.isView(e)) {
        return (0 !== e.byteOffset || e.byteLength !== e.buffer.byteLength) && (e = e instanceof DataView ? new DataView(e.buffer.slice(e.byteOffset, e.byteOffset + e.byteLength), 0, e.byteLength) : e.slice()), { buffer: e.buffer, __typedArray: e.constructor.name };
      }
      return Object.entries(e).forEach(([t, n]) => {
        e[t] = p(n);
      }), e;
    case "symbol":
      return `__symbol:${Symbol.keyFor(e) || e.toString()}`;
    case "function":
      return;
    case "number":
    case "string":
    case "undefined":
    case "boolean":
      return e;
  }
}
const m = "__message_data_no_transfer";
class b {
  static toRaw(e) {
    try {
      return "object" == typeof e && m in e ? (delete e[m], e) : g(e);
    } finally {
    }
  }
  static format(e, t = false) {
    try {
      return "object" == typeof e ? t ? p(e) : (e[m] = true, e) : p(e);
    } finally {
    }
  }
}
var y, v;
const w = _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.u("EventTarget"), _ = _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.u("Event"), O = _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.u("MessageEvent");
class x extends w {
  static [Symbol.hasInstance](e) {
    return _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.p(this, e, _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.d(this, y, "f", v));
  }
  [Symbol.toStringTag]() {
    return "Worker";
  }
  constructor(e, t) {
    super(), Object.defineProperty(this, "_worker", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "_onmessage", { enumerable: true, configurable: true, writable: true, value: void 0 }), Object.defineProperty(this, "_onerror", { enumerable: true, configurable: true, writable: true, value: void 0 }), this.addEventListener("message", (e2) => {
      var t2;
      null === (t2 = this._onmessage) || void 0 === t2 || t2.call(this, e2);
    }), this.addEventListener("error", (e2) => {
      var t2;
      null === (t2 = this._onerror) || void 0 === t2 || t2.call(this, e2);
    }), y.preWorkerInstance && y.preWorkerInstance.terminate();
    const n = _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm.wx$1.createWorker(e, t);
    n ? (this._worker = n, n.onMessage((e2) => {
      const t2 = new O("message", { data: b.toRaw(e2) });
      this.dispatchEvent(t2);
    }), n.onProcessKilled((e2) => {
      this.dispatchEvent(new _("processkilled"));
    }), y.preWorkerInstance = n) : this.dispatchEvent(new _("error"));
  }
  set onmessage(e) {
    "function" == typeof e && (this._onmessage = e);
  }
  set onerror(e) {
    "function" == typeof e && (this._onerror = e);
  }
  get onmessage() {
    return this._onmessage;
  }
  get onerror() {
    return this._onerror;
  }
  postMessage(e, t) {
    var n;
    null === (n = this._worker) || void 0 === n || n.postMessage(b.format(e, !!t));
  }
  terminate() {
    var e;
    null === (e = this._worker) || void 0 === e || e.terminate(), y.preWorkerInstance && (y.preWorkerInstance = void 0);
  }
}
var I, j;
y = x, Object.defineProperty(x, "MessageData", { enumerable: true, configurable: true, writable: true, value: b }), v = { value: /* @__PURE__ */ new WeakSet() };
let k = false;
class W {
  static [Symbol.hasInstance](e) {
    return _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.d(this, I, "f", j).has(e);
  }
  constructor() {
    k || (!function() {
      const e2 = _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm.wx$1.createWebAudioContext(), t2 = e2.createGain().gain, n = Object.getPrototypeOf(t2);
      ["setValueAtTime", "setTargetAtTime", "linearRampToValueAtTime"].forEach((e3) => {
        const t3 = n[e3];
        n[e3] = function(...e4) {
          try {
            t3.apply(this, e4);
          } catch (t4) {
            this.value = e4[0];
          }
        };
      }), e2.close();
    }(), k = true);
    const e = _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm.wx$1.createWebAudioContext();
    _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.d(I, I, "f", j).add(e);
    const t = e.decodeAudioData;
    return e.decodeAudioData = function(n, r, a) {
      return new Promise((o, s) => {
        t.call(e, n, (e2) => {
          r && r(e2), o(e2);
        }, (e2) => {
          a && a(e2), s(e2);
        });
      });
    }, e;
  }
}
I = W, j = { value: /* @__PURE__ */ new WeakSet() };
const A = "triggerGC" in _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm.wx$1;
var E, P, S, C, T, D;
const L = (() => {
  const e = A ? null : _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm.wx$1.createOffscreenCanvas({ width: 1, height: 1, type: "2d" });
  return { createImage: () => A ? _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm.wx$1.createImage() : e.createImage(), createImageData: (...t) => A ? _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm.wx$1.createImageData(...t) : e.createImageData(...t), createBaseCanvas(e2, t, n) {
    if (A) {
      const n2 = _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm.wx$1.createCanvas();
      return n2.width = null != e2 ? e2 : 1, n2.height = null != t ? t : 1, n2;
    }
    {
      const r = _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm.wx$1.createOffscreenCanvas({ width: null != e2 ? e2 : 1, height: null != t ? t : 1, type: null != n ? n : _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.b.defaultCanvasContextType });
      return r.width = null != e2 ? e2 : 1, r.height = null != t ? t : 1, r;
    }
  }, requestAnimationFrame: A ? requestAnimationFrame : e.requestAnimationFrame, cancelAnimationFrame: A ? cancelAnimationFrame : e.cancelAnimationFrame };
})(), B = /* @__PURE__ */ new WeakSet();
function F(e) {
  B.add(e);
}
const R = "__patched_canvas__";
function M(t, n, r) {
  const a = Object.hasOwn(t, "getContext") ? t.getContext : null;
  a && delete t.getContext;
  const { element: o, updateRectInfo: c } = h(t, n, r, (n2, r2) => ({ getContext: { value: (...o2) => {
    const s = (a || r2.getContext).call(t, ...o2);
    "webgl2" === o2[0] && _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.j(s);
    const c2 = s.canvas;
    if (!(R in c2)) {
      try {
        F(c2), _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.E(c2), Object.defineProperty(c2, "style", { value: n2, writable: false });
      } catch (e) {
      }
      c2[R] = true;
    }
    return s;
  } }, toBlob: { value: (e, n3, r3) => {
    _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.v(t, n3, r3).then((t2) => {
      e(t2);
    });
  } } })), l = o.getBoundingClientRect();
  return t.width !== l.width && (t.width = l.width), t.height !== l.height && (t.height = l.height), F(o), { canvas: o, updateRectInfo: c };
}
class q {
  static [Symbol.hasInstance](e) {
    return _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.p(this, e, B);
  }
  constructor() {
    throw new TypeError("Illegal constructor");
  }
}
class V {
  static [Symbol.hasInstance](e) {
    return _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.p(this, e, _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.d(this, E, "f", P));
  }
  constructor() {
    const t = _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.u("Event"), n = _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.E(L.createImage());
    n.onload = (e) => {
      n.dispatchEvent(new t("load"));
    }, n.onerror = (e) => {
      n.dispatchEvent(new t(""));
    };
    const o = Object.getOwnPropertyDescriptor(Object.getPrototypeOf(n), "src");
    if (!o) throw Error("something wrong in get image src descriptor");
    const { get: s, set: i } = o;
    return Object.defineProperties(n, { naturalWidth: { get: () => n.width }, naturalHeight: { get: () => n.height }, src: { enumerable: true, configurable: false, get: s, set(e) {
      (e = String(e)).startsWith(_mpChunkDeps__minisheep_miniProgramPolyfillCore_dist_wechat.ne) ? i.call(n, _mpChunkDeps__minisheep_miniProgramPolyfillCore_dist_wechat.ce(e)) : i.call(n, e);
    } } }), _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.d(E, E, "f", P).add(n), n;
  }
}
E = V, P = { value: /* @__PURE__ */ new WeakSet() };
class X {
  static [Symbol.hasInstance](e) {
    return _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.p(this, e, _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.d(this, S, "f", C));
  }
  constructor(e, t) {
    const n = new V();
    return void 0 !== e && (n.width = e), void 0 !== t && (n.height = t), _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.d(S, S, "f", C).add(n), n;
  }
}
S = X, C = { value: /* @__PURE__ */ new WeakSet() };
class Y {
  static [Symbol.hasInstance](e) {
    return _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.p(this, e, _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.d(this, T, "f", D));
  }
  constructor(...e) {
    const t = L.createImageData(...e);
    return _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.d(T, T, "f", D).add(t), t;
  }
}
T = Y, D = { value: /* @__PURE__ */ new WeakSet() };
class $ {
  static [Symbol.hasInstance](e) {
    return _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.p(this, e, B);
  }
  constructor(e, t) {
    return function(e2, t2, n) {
      const { canvas: r } = M(L.createBaseCanvas(e2, t2, n));
      return r;
    }(e, t, _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.b.defaultCanvasContextType);
  }
}
const H = L.requestAnimationFrame, G = L.cancelAnimationFrame;
function K(e, t) {
  return (n) => {
    let r, a;
    const o = (t2) => {
      var s, i;
      if (void 0 === r) r = t2 || (null === (s = globalThis.performance) || void 0 === s ? void 0 : s.now()) || Date.now();
      else {
        const e2 = t2 || (null === (i = globalThis.performance) || void 0 === i ? void 0 : i.now()) || Date.now(), a2 = e2 - r;
        r = e2;
        try {
          n(a2);
        } catch (e3) {
          console.error("error in frame: ", e3);
        }
      }
      a = e(o);
    };
    return a = e(o), { cancel: () => t(a) };
  };
}
const N = /* @__PURE__ */ new WeakMap();
function z(e, t, n, a = true) {
  const o = _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.u("PointerEvent"), s = { touchstart: "pointerdown", touchmove: "pointermove", touchend: "pointerup", touchcancel: "pointercancel", tap: "click" }[e.type] || "", i = Array.from(e.changedTouches);
  if (i.length) {
    let r, l;
    N.has(n) ? l = N.get(n) : (l = {}, N.set(n, l));
    const u = l[i[0].identifier];
    (!a || u && u.expired < Date.now() || "touchstart" === e.type || "tap" === e.type && !u) && (r = Promise.resolve(n()), l[i[0].identifier] = { p: r, expired: Date.now() + 1e3 }, _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.w("init point rectInfo ", e, N.get(n))), !r && i[0].identifier in l && (_mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.w("try use stored rectInfo", e, l), r = l[i[0].identifier].p), r || (r = Promise.resolve(n()), l[i[0].identifier] = { p: r, expired: Date.now() + 1e3 }), r.then((n2) => {
      i.forEach((r2, a2) => {
        const i2 = new o(s, { pointerId: r2.identifier, pointerType: "touch", offsetX: r2.x, offsetY: r2.y, clientX: "clientX" in r2 ? r2.clientX : (r2.x || 0) + n2.left, clientY: "clientY" in r2 ? r2.clientY : (r2.y || 0) + n2.top, button: "pointermove" === s ? -1 : 0, isPrimary: 0 === a2 });
        [t].flat().filter((e2) => !!e2).forEach((t2) => {
          try {
            _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.w(`dispatchEvent ${e.type} -> ${i2.type}`, e, i2, t2), "function" == typeof t2.dispatchEvent && t2.dispatchEvent(i2), "pointercancel" === i2.type && setTimeout(() => {
              "function" == typeof t2.dispatchEvent && t2.dispatchEvent(i2);
            }, 100);
          } catch (e2) {
            console.error(e2);
          }
        });
      });
    });
  }
}
exports.$ = $;
exports.G = G;
exports.H = H;
exports.K = K;
exports.M = M;
exports.V = V;
exports.W = W;
exports.X = X;
exports.Y = Y;
exports.d = d;
exports.h = h;
exports.q = q;
exports.x = x;
exports.z = z;
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/_mpChunkDeps/@minisheep/three-platform-adapter/dist/eventManager.js.map
