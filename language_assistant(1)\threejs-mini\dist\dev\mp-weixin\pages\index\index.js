"use strict";
const _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm = require("../../common/chunks/@dcloudio/uni-mp-weixin/dist/uni.api.esm.js");
const _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm = require("../../common/chunks/@dcloudio/uni-mp-vue/dist/vue.runtime.esm.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = /* @__PURE__ */ _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm.defineComponent({
  __name: "index",
  setup(__props) {
    const title = _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm.ref("Hello Three.js");
    function navigateTo(e) {
      _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm.index.navigateTo({
        url: e.target.dataset.target
      });
    }
    return (_ctx, _cache) => {
      return {
        a: common_assets._imports_0,
        b: _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm.t(title.value),
        c: _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm.o(navigateTo),
        d: _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm.o(navigateTo)
      };
    };
  }
});
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map
