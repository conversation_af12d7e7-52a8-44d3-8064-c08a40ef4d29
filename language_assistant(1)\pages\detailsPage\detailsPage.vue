<template>
	<CustomNavbar :title="(t('home.aiLanguageAssistant'))" back />
	<!-- 详情页 -->
	<view class="page-details">
		<!-- 3D数字人模型区域 -->
		<view class="digital-human-3d-container">
			<!-- 使用PlatformCanvas组件 -->
			<PlatformCanvas
				v-if="!showFallback"
				type="webgl2"
				canvas-id="webgl-3d-model"
				@useCanvas="useCanvas"
				@touchstart="onTouchStart"
				@touchmove="onTouch3DMove"
				@touchend="onTouchEnd"
				class="three-canvas"
			/>

			<view class="loading-overlay" v-if="isModelLoading">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载3D数字人中...</text>
			</view>

			<!-- 备用显示：Teacher数字人图像 -->
			<view class="fallback-container" v-if="showFallback">
				<view class="teacher-avatar">
					<view class="avatar-circle">
						<text class="avatar-emoji">👨‍🏫</text>
					</view>
					<view class="avatar-glow"></view>
				</view>
				<view class="teacher-info">
					<text class="teacher-name">AI Teacher</text>
					<text class="teacher-subtitle">语言学习助手</text>
				</view>
			</view>
		</view>

		<!-- 自定义导航栏 -->
		<view class="content" :class="appStores.lang=='zh-Ug'?'ug':''">
			<view class="options-row">
				<!-- 语言选择 -->
				<view class="option-item">
					<image src="/static/home/<USER>" mode="aspectFit" class="icon"></image>
					<text class="label-text">{{appStores.lang === 'zh-Ug' ? languagename_uy : languagename}}</text>
				</view>
				<!-- 定位选择 -->
				<view class="option-item-location">
					<text class="label">{{$t('detailsPage.location')}}：</text>
					<uni-data-select v-model="locationValue" :localdata="locationRange" :clear="false"
						class="select-box"></uni-data-select>
				</view>
				<uni-icons type="right" size="20" color="#006397" class="arrow-right"></uni-icons>
				<!-- 场景选择 -->
				<view class="option-item-location">
					<text class="label">{{$t('detailsPage.scene')}}：</text>
					<uni-data-select v-model="sceneValue" :localdata="sceneRange" :clear="false"
						class="select-box"></uni-data-select>
				</view>
			</view>
			<!-- 信息 -->
			<view class="Plate">
				<view class="Plate-nav">
					<text class="Plate-Student">学生家长 : </text>
					<text class="Plate-name"> 买买提</text>
				</view>
				<view class="Plate-nav">
					<text class="Plate-Training">{{$t('detailsPage.startTraining')}} : </text>
					<text class="Plate-name">参加学生家长会</text>
				</view>
			</view>
			<!-- <view class="Training-nav">
				<img src="/static/home/<USER>" alt="" class="Training-icon">
				<view class="Training">第一次训练在进行</view>
			</view> -->
			<view class="focus-line"></view>
			<!-- 数字人头像显示区域 -->
			<view class="avatar-section">
				<view class="avatar-container">
					<image src="../../static/home/<USER>" class="avatar-image" />
					<image src="../../static/icons/fangda.svg" class="zoom-icon" />
				</view>
			</view>
			<!-- 实时对话区域 -->
			<view class="chat-container">
				<scroll-view class="chat-messages" scroll-y :scroll-into-view="scrollToMessage"
					:scroll-with-animation="true" @scrolltoupper="onScrollToUpper">
					<template v-for="(msg, idx) in messages" :key="idx">
						<!-- 用户消息 -->
						<view v-if="msg.sender === 'user'" class="bubble-row user-row" :id="'msg-' + idx">
							<image v-if="msg.avatar" :src="msg.avatar" class="avatar-user"></image>
							<view class="bubble-cn user-bubble">
								<text v-if="msg.type !== 'voice'" class="bubble-cn-text">{{ msg.content }}</text>
								<view v-else class="voice-message" @tap="playVoice(msg)">
									<uni-icons type="sound" size="24" color="#222"></uni-icons>
									<text class="voice-text">{{ msg.duration }}s</text>
									<view v-if="msg.unread" class="unread-dot"></view>
								</view>
							</view>
						</view>
						<!-- 系统/后端消息（只显示中文） -->
						<view v-else-if="msg.sender === 'system'" class="bubble-row" :id="'msg-' + idx">
							<view class="message-icon-container">
								<image src="/static/icons/jiqiren.svg" class="message-icon"></image>
							</view>
							<view class="bubble-cn">
								<text v-if="msg.type !== 'voice'" class="bubble-cn-text">{{ msg.content }}</text>
								<view v-else class="voice-message" @tap="playVoice(msg)">
									<uni-icons type="sound" size="24" color="#1677c7"></uni-icons>
									<text class="voice-text">{{ msg.duration }}s</text>
									<view v-if="msg.unread" class="unread-dot"></view>
								</view>
							</view>
						</view>
					</template>
				</scroll-view>
			</view>
			<!-- 选择对话部分 -->
			<!-- 底部操作栏 -->
			<view class="bottom-bar">
				<scroll-view class="category-buttons" scroll-x="true" show-scrollbar="false">
					<view v-for="(item, index) in categories" :key="item.key" class="category-button"
						:class="{ 'active': activeCategory === item.key }" @click="switchCategory(item.key)">
						{{ item.label }}
					</view>
				</scroll-view>
				<view class="input-wrapper">
					<view class="mode-switch" @click="switchInputMode">
						<image :src="isTextMode ? '/static/icons/jianpan.svg' : '/static/icons/maikefeng.svg'"
							class="mode-icon" mode="aspectFit"></image>
					</view>
					<!-- 文字输入模式 -->
					<view v-if="isTextMode" class="input-box">
						<input type="text" :placeholder="$t('detailsPage.inputMessage')" v-model="inputMessage"
							class="message-input" />
					</view>
					<!-- 语音输入模式 -->
					<view v-else class="voice-btn" @touchstart.prevent="startRecord" @touchend.prevent="stopRecord"
						@touchcancel.prevent="cancelRecord" @touchmove.prevent="onTouchMove">
						<text class="voice-btn-text">{{$t('detailsPage.holdToSpeak')}}</text>
					</view>
					<button class="send-btn" @click="sendMessage">{{$t('detailsPage.send')}}</button>
				</view>
			</view>

			<!-- 录音中的浮层 -->
			<view class="recording-overlay" v-if="isRecording">
				<view class="recording-box" :class="{'cancel-recording': willCancel}">
					<view class="recording-icon-wrapper">
						<image v-if="willCancel" src="/static/icons/akigakayturux.svg" class="cancel-icon"></image>
						<view v-else class="recording-waves">
							<view class="wave" v-for="i in 3" :key="i" :style="{animationDelay: (i-1)*0.2+'s'}"></view>
						</view>
					</view>
					<text
						class="recording-status">{{willCancel ? $t('detailsPage.releaseToCancel') : $t('detailsPage.releaseToSend')}}</text>
					<text class="recording-tip">{{$t('detailsPage.slideUpToCancel')}}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		reactive,
		nextTick,
		watch,
		onMounted,
		onUnmounted,
		getCurrentInstance
	} from 'vue'
	// 导入Three.js (在微信小程序中使用)
	import * as THREE from 'three'
	import {
		useUserStore
	} from '@/stores/counter.js'
	import {
		onShow,
		onLoad
	} from '@dcloudio/uni-app'
	import {
		useI18n
	} from 'vue-i18n'
	const {
		t
	} = useI18n()
	import {
		scholars_scenes
	} from '/request/api.js'
	import {
		appStore
	} from '@/stores/app.js'
	import PlatformCanvas from '@/components/PlatformCanvas.vue'

	const store = useUserStore();
	const appStores = appStore();
	// 下面才是变量、函数、逻辑

	// 3D模型显示相关变量
	const isModelLoading = ref(true); // 3D模型加载状态
	const showFallback = ref(false); // 控制是否显示备用图像

	// Three.js相关变量
	const threeData = reactive({
		renderer: null,
		scene: null,
		camera: null,
		model: null,
		mixer: null,
		clock: null,
		animationId: null,
		controls: null
	});

	// 触摸交互变量
	const touchData = reactive({
		lastX: 0,
		lastY: 0,
		isRotating: false
	});

	const locationRange = ref([]); // 定位下拉框
	const sceneRange = ref([]); // 场景下拉框
	const agent_list = ref({}); // 关于选择智能体部分

	const locationValue = ref(1);
	const sceneValue = ref(1);
	// 录音相关状态
	const isRecording = ref(false);
	const recorderManager = uni.getRecorderManager();
	const innerAudioContext = uni.createInnerAudioContext();
	const recordStartTime = ref(0);
	const willCancel = ref(false); // 是否将要取消录音
	const touchStartY = ref(0); // 记录触摸开始位置的Y坐标

	// 输入模式相关状态
	const isTextMode = ref(true);
	// 添加滚动相关的状态
	const scrollToMessage = ref('');

	const languagename = ref('普通话'); // 默认给的数字人昵称
	const languagename_uy = ref(''); // 维语名称
	const title = ref('语言助手'); // 页面标题
	//输入框
	const inputMessage = ref('');
	const messages = ref([{
		sender: 'system',
		avatar: '/static/mine/avatar.jpg',
		content: '你好！咱们开始普通话训练吧！'
	}]);

	const categories = [{
			key: 'dialog',
			label: t('detailsPage.switchDialog')
		},
		{
			key: 'level',
			label: t('detailsPage.adjustDifficulty')
		},
		{
			key: 'vocab',
			label: t('detailsPage.vocabularyExplanation')
		},
		{
			key: 'grammar',
			label: t('detailsPage.grammarExplanation')
		},
		{
			key: 'hello',
			label: t('detailsPage.hello')
		},
		{
			key: 'iAmGood',
			label: t('detailsPage.iAmGood')
		},
		{
			key: 'goodMorning',
			label: t('detailsPage.goodMorning')
		}
	];

	const activeCategory = ref('dialog'); // 当前激活的对话类别 // 当前激活的对话类别

	const pageAgentId = ref(null); // 新增变量用于存储 id

	// 监听消息变化，自动滚动到底部
	watch(messages, async () => {
		await nextTick();
		scrollToBottom();
	}, {
		deep: true
	});

	// 滚动到底部的方法
	const scrollToBottom = () => {
		nextTick(() => {
			if (messages.value.length > 0) {
				scrollToMessage.value = 'msg-' + (messages.value.length - 1);
			}
		});
	};

	// 监听滚动到顶部事件
	const onScrollToUpper = () => {
		console.log('滚动到顶部');
		// 这里可以添加加载历史消息的逻辑
	};

	// 修改发送消息的方法
	const sendMessage = () => {
		if (!inputMessage.value.trim()) {
			return; // 如果消息为空，直接返回
		}
		// 用户消息
		messages.value.push({
			sender: 'user',
			avatar: '/static/mine/avatar.jpg',
			content: inputMessage.value
		});
		const userMsg = inputMessage.value;
		inputMessage.value = '';
		// 模拟后端回复，实际可替换为API
		setTimeout(() => {
			messages.value.push({
				sender: 'system',
				avatar: '/static/mine/avatar.jpg',
				content: '收到你的信息：' + userMsg
			});
		}, 800);
	};



	// 播放语音消息
	const playVoice = (msg) => {
		innerAudioContext.src = msg.voiceUrl;
		innerAudioContext.play();
		// 标记消息为已读
		if (msg.unread) {
			msg.unread = false;
		}
		// 监听播放结束
		innerAudioContext.onEnded(() => {
			console.log('语音播放结束');
		});
	};

	// 开始录音
	const startRecord = (e) => {
		touchStartY.value = e.changedTouches[0].clientY;
		willCancel.value = false;
		recordStartTime.value = Date.now();
		recorderManager.start({
			format: 'mp3',
			duration: 60000, // 最长录音时间，单位ms
			sampleRate: 16000, // 采样率
			numberOfChannels: 1, // 录音通道数
			encodeBitRate: 48000, // 编码码率
		});
		isRecording.value = true;
	};

	// 触摸移动处理
	const onTouchMove = (e) => {
		if (isRecording.value) {
			const currentY = e.changedTouches[0].clientY;
			const moveDistance = touchStartY.value - currentY;

			// 当上滑距离超过50像素时，认为是要取消录音
			willCancel.value = moveDistance > 50;
		}
	};

	// 停止录音
	const stopRecord = () => {
		if (isRecording.value) {
			if (willCancel.value) {
				cancelRecord();
			} else {
				recorderManager.stop();
				isRecording.value = false;
			}
		}
	};

	// 取消录音
	const cancelRecord = () => {
		if (isRecording.value) {
			recorderManager.stop();
			isRecording.value = false;
			willCancel.value = false;
			uni.showToast({
				title: $t('detailsPage.recordCanceled'),
				icon: 'none'
			});
		}
	};

	// 录音结束事件处理
	recorderManager.onStop((res) => {
		console.log('录音结束', res);
		isRecording.value = false;
		willCancel.value = false;

		// 如果是主动取消录音，不添加到消息列表
		if (!willCancel.value) {
			// 计算录音时长（秒）
			const duration = Math.round((Date.now() - recordStartTime.value) / 1000);

			// 将录音添加到消息列表
			messages.value.push({
				sender: 'user',
				avatar: '/static/mine/avatar.jpg',
				content: '[语音消息]',
				type: 'voice',
				voiceUrl: res.tempFilePath,
				duration: duration,
				unread: true
			});

			// 模拟系统回复
			setTimeout(() => {
				messages.value.push({
					sender: 'system',
					avatar: '/static/mine/avatar.jpg',
					content: '收到你的语音消息',
					type: 'voice',
					voiceUrl: res.tempFilePath,
					duration: duration,
					unread: true
				});
			}, 800);
		}
	});

	// 录音错误处理
	recorderManager.onError((res) => {
		console.error('录音错误:', res);
		isRecording.value = false;
		willCancel.value = false;
		uni.showToast({
			title: '录音失败',
			icon: 'none'
		});
	});



	// 切换输入模式
	const switchInputMode = () => {
		isTextMode.value = !isTextMode.value;
	};

	// 显示表情选择
	const showEmoji = () => {
		// 实现显示表情选择的逻辑
	};

	// 显示更多选项
	const showMore = () => {
		// 实现显示更多选项的逻辑
	};

	// Web-view相关方法
	const onWebViewMessage = (e) => {
		console.log('收到web-view消息:', e.detail.data);

		if (e.detail && e.detail.data) {
			const data = e.detail.data;

			if (data.type === 'model-loaded') {
				isModelLoading.value = false;
				if (data.success) {
					console.log('3D模型通过web-view加载成功');
					showFallback.value = false;
				} else {
					console.log('3D模型加载失败，显示备用图像');
					showFallback.value = true;
				}
			}
		}
	};

	const onWebViewError = (e) => {
		console.error('Web-view加载失败:', e);
		isModelLoading.value = false;
		showFallback.value = true;

		uni.showToast({
			title: '3D模型加载失败，使用备用显示',
			icon: 'none',
			duration: 2000
		});
	};

	// 切换对话类别
	const switchCategory = (key) => {
		activeCategory.value = key;
		// 根据选中的类别执行不同的操作，例如加载不同的对话内容
		console.log('切换到类别:', key);
	};

	// Live2D相关事件处理
	const onLive2dLoad = () => {
		console.log('Live2D加载成功');
		showFallback.value = false; // 加载成功，隐藏备用图像
		uni.showToast({
			title: 'Live2D数字人加载成功',
			icon: 'success',
			duration: 2000
		});
	};

	const onLive2dError = (e) => {
		console.error('Live2D加载失败:', e);
		showFallback.value = true; // 显示备用图像
		uni.showToast({
			title: '数字人加载失败，使用备用显示',
			icon: 'none',
			duration: 3000
		});
	};

	const onLive2dMessage = (e) => {
		console.log('Live2D消息:', e);
	};



	// 获取学者和场景数据，支持传入 agent_id
	const getScholarsAndScenes = async (agent_id) => {
		try {
			const res = await scholars_scenes(agent_id);
			// console.log('scholars_scenes返回：', res);
			// 数据格式化，确保下拉框显示 name，value 为 id
			locationRange.value = (res.data.scholars || []).map(item => ({
				value: item.id,
				text: appStores.lang === 'zh-Ug' ? item.name_uy : item.name
			}));
			sceneRange.value = (res.data.scenes || []).map(item => ({
				value: item.id,
				text: appStores.lang === 'zh-Ug' ? item.name_uy : item.name
			}));
		} catch (e) {
			uni.showToast({
				title: '获取下拉数据失败',
				icon: 'none'
			});
		}
	};
	onShow(() => {
		getScholarsAndScenes(pageAgentId.value);
	});


	onLoad((options) => {
		const id = options.id;
		// console.log('接收到的id:', id);
		pageAgentId.value = id; // 存储 id 供 onShow 使用

		const name = options.name ? decodeURIComponent(options.name) : '';
		// console.log('传递过来的name:', name);
		if (name) {
			languagename.value = name;
		}

		const name_uy = options.name_uy ? decodeURIComponent(options.name_uy) : '';
		// console.log('传递过来的name_uy:', name_uy);
		if (name_uy) {
			languagename_uy.value = name_uy;
		}
	});

	// PlatformCanvas回调函数 - 完全按照参考项目方式
	function useCanvas({ canvas, useFrame, recomputeSize }) {
		console.log('[参考项目方式] Canvas初始化成功:', canvas);
		console.log('[参考项目方式] Canvas尺寸:', { width: canvas.width, height: canvas.height });

		const CANVAS_WIDTH = canvas.width;
		const CANVAS_HEIGHT = canvas.height;
		let camera, scene, renderer;

		init();
		render();

		function init() {
			try {
				console.log('[参考项目方式] 开始初始化Three.js...');

				// 完全按照参考项目的方式创建渲染器
				renderer = new THREE.WebGLRenderer({ canvas, antialias: true });
				renderer.setPixelRatio(window.devicePixelRatio || 1);
				renderer.setSize(CANVAS_WIDTH, CANVAS_HEIGHT);
				renderer.toneMapping = THREE.ACESFilmicToneMapping;
				renderer.toneMappingExposure = 1;

				camera = new THREE.PerspectiveCamera(45, CANVAS_WIDTH / CANVAS_HEIGHT, 1, 2000);
				camera.position.set(0, 100, 0);

				scene = new THREE.Scene();
				scene.background = new THREE.Color(0xf5f7fa);

				// 添加光源
				const ambientLight = new THREE.AmbientLight(0xffffff, 0.8);
				scene.add(ambientLight);

				const directionalLight = new THREE.DirectionalLight(0xffffff, 0.6);
				directionalLight.position.set(5, 10, 5);
				scene.add(directionalLight);

				// 加载GLB模型
				loadGLBModel('https://reader.c8plus.net/statix/Teacher.glb');

				console.log('[参考项目方式] Three.js初始化成功');

				// 隐藏加载状态
				showFallback.value = false;
				isModelLoading.value = false;

			} catch (error) {
				console.error('[参考项目方式] Three.js初始化失败:', error);
				showFallback.value = true;
				isModelLoading.value = false;
			}
		}

		function render() {
			if (renderer && scene && camera) {
				renderer.render(scene, camera);
			}
		}

		function loadGLBModel(modelUrl) {
			import('three/examples/jsm/loaders/GLTFLoader.js').then(({ GLTFLoader }) => {
				const loader = new GLTFLoader();

				console.log('[参考项目方式] 开始加载模型:', modelUrl);

				loader.load(modelUrl, function (gltf) {
					const model = gltf.scene;
					const animations = gltf.animations;

					console.log('[参考项目方式] 模型加载成功', { model, animations });

					// 调整模型大小和位置
					model.scale.set(220, 220, 220);
					model.position.set(10, 50, 0);

					scene.add(model);
					render();

					console.log('[参考项目方式] 模型添加到场景成功');
				}, undefined, (error) => {
					console.error('[参考项目方式] 模型加载失败:', error);
					showFallback.value = true;
					isModelLoading.value = false;
				});
			}).catch((error) => {
				console.error('[参考项目方式] GLTFLoader导入失败:', error);
				showFallback.value = true;
				isModelLoading.value = false;
			});
		}
	}

	// 初始化Teacher数字人显示
	const initMiniCanvas = async () => {
		console.log('初始化Teacher数字人显示');

		// 在微信小程序环境中，直接显示Teacher数字人备用图像
		// 这样可以确保用户看到一个漂亮的Teacher头像
		showFallback.value = true;
		isModelLoading.value = false;

		console.log('Teacher数字人显示初始化完成');
	};

	// 初始化Three.js场景
	const initThreeScene = (canvas, useFrame, recomputeSize) => {
		try {
			console.log('[微信小程序] 开始初始化Three.js场景...');
			console.log('[微信小程序] Canvas信息:', {
				width: canvas.width,
				height: canvas.height,
				platform: uni.getSystemInfoSync().platform
			});

			const CANVAS_WIDTH = canvas.width;
			const CANVAS_HEIGHT = canvas.height;

			// 检查canvas尺寸
			if (CANVAS_WIDTH === 0 || CANVAS_HEIGHT === 0) {
				console.error('[微信小程序] Canvas尺寸为0，无法初始化Three.js');
				showFallback.value = true;
				isModelLoading.value = false;
				return;
			}

			// 为微信小程序canvas添加必要的方法
			if (!canvas.addEventListener) {
				canvas.addEventListener = () => {};
			}
			if (!canvas.removeEventListener) {
				canvas.removeEventListener = () => {};
			}
			if (!canvas.clientWidth) {
				canvas.clientWidth = CANVAS_WIDTH;
			}
			if (!canvas.clientHeight) {
				canvas.clientHeight = CANVAS_HEIGHT;
			}

			// 创建渲染器
			threeData.renderer = new THREE.WebGLRenderer({
				canvas,
				antialias: true,
				alpha: true,
				preserveDrawingBuffer: true, // 微信小程序需要
				powerPreference: "high-performance"
			});
			threeData.renderer.setPixelRatio(uni.getSystemInfoSync().pixelRatio || 1);
			threeData.renderer.setSize(CANVAS_WIDTH, CANVAS_HEIGHT);
			threeData.renderer.toneMapping = THREE.ACESFilmicToneMapping;
			threeData.renderer.toneMappingExposure = 1;

			console.log('[微信小程序] WebGL渲染器创建成功');

			// 创建相机 - 适合页面上三分之一显示
			threeData.camera = new THREE.PerspectiveCamera(50, CANVAS_WIDTH / CANVAS_HEIGHT, 1, 1000);
			threeData.camera.position.set(0, 40, 100); // 调整相机位置适合小程序显示

			// 创建场景
			threeData.scene = new THREE.Scene();
			threeData.scene.background = new THREE.Color(0xf5f7fa);

			// 添加环境光
			const ambientLight = new THREE.AmbientLight(0xffffff, 0.9);
			threeData.scene.add(ambientLight);

			// 添加方向光
			const directionalLight = new THREE.DirectionalLight(0xffffff, 0.5);
			directionalLight.position.set(3, 8, 5);
			threeData.scene.add(directionalLight);

			// 添加补充光源
			const fillLight = new THREE.DirectionalLight(0xffffff, 0.3);
			fillLight.position.set(-3, 5, -5);
			threeData.scene.add(fillLight);

			// 创建时钟
			threeData.clock = new THREE.Clock();

			console.log('[微信小程序] Three.js场景初始化成功');

			// 开始渲染循环
			startRenderLoop();

			// 加载GLB模型
			loadGLBModel();

		} catch (error) {
			console.error('[微信小程序] Three.js场景初始化失败:', error);
			showFallback.value = true;
			isModelLoading.value = false;
		}
	};



	// 加载GLB模型
	const loadGLBModel = () => {
		try {
			console.log('开始加载GLB模型...');

			// 在微信小程序中导入GLTFLoader
			import('three/examples/jsm/loaders/GLTFLoader.js').then(({ GLTFLoader }) => {
				console.log('GLTFLoader导入成功');
				const loader = new GLTFLoader();

				// 模型路径 - 使用您提供的后端模型URL
				const modelPaths = [
					'https://reader.c8plus.net/statix/Teacher.glb', // 您提供的后端模型
					'http://minhandash.edu.izdax.cn/Teacher_001.glb', // 备用网络URL
					'/static/Teacher.glb' // 本地路径
				];

				const tryLoadModel = (pathIndex = 0) => {
					if (pathIndex >= modelPaths.length) {
						console.error('所有模型路径都加载失败，显示备用图像');
						showFallback.value = true;
						isModelLoading.value = false;
						return;
					}

					const modelPath = modelPaths[pathIndex];
					console.log(`[微信小程序] 尝试加载GLB模型 (${pathIndex + 1}/${modelPaths.length}):`, modelPath);

					loader.load(
						modelPath,
						(gltf) => {
							console.log('[微信小程序] GLB模型加载成功!', gltf);

							threeData.model = gltf.scene;

							// 调整模型大小和位置（适合页面上三分之一显示）
							threeData.model.scale.set(120, 120, 120); // 适合小程序显示的尺寸
							threeData.model.position.set(0, -20, 0);

							// 将模型添加到场景
							threeData.scene.add(threeData.model);

							// 设置动画
							if (gltf.animations && gltf.animations.length > 0) {
								threeData.mixer = new THREE.AnimationMixer(threeData.model);
								console.log(`[微信小程序] 找到 ${gltf.animations.length} 个动画`);

								// 播放第一个动画
								const action = threeData.mixer.clipAction(gltf.animations[0]);
								action.play();
							}

							console.log('[微信小程序] Teacher 3D模型加载并显示成功!');
							showFallback.value = false;
							isModelLoading.value = false;
						},
						(progress) => {
							if (progress.total > 0) {
								const percent = (progress.loaded / progress.total * 100).toFixed(1);
								console.log(`[微信小程序] 模型加载进度: ${percent}%`);
							}
						},
						(error) => {
							console.error(`[微信小程序] GLB模型加载失败 (路径 ${pathIndex + 1}):`, error);
							// 尝试下一个路径
							setTimeout(() => {
								tryLoadModel(pathIndex + 1);
							}, 1000);
						}
					);
				};

				// 开始尝试加载
				tryLoadModel();

			}).catch((error) => {
				console.error('[微信小程序] GLTFLoader导入失败:', error);
				showFallback.value = true;
				isModelLoading.value = false;
			});
		} catch (error) {
			console.error('[微信小程序] GLB模型加载初始化失败:', error);
			showFallback.value = true;
			isModelLoading.value = false;
		}
	};

	// 渲染循环
	const startRenderLoop = () => {
		const render = () => {
			if (!threeData.renderer || !threeData.scene || !threeData.camera) return;

			// 更新动画混合器
			if (threeData.mixer && threeData.clock) {
				const delta = threeData.clock.getDelta();
				threeData.mixer.update(delta);
			}

			// 渲染场景
			threeData.renderer.render(threeData.scene, threeData.camera);

			// 继续下一帧
			threeData.animationId = requestAnimationFrame(render);
		};

		render();
	};





	// 触摸交互功能
	const onTouchStart = (e) => {
		console.log('3D模型触摸开始', e);
		if (e.touches && e.touches.length > 0) {
			touchData.lastX = e.touches[0].clientX;
			touchData.lastY = e.touches[0].clientY;
			touchData.isRotating = true;
		}
	};

	const onTouch3DMove = (e) => {
		if (!touchData.isRotating || !e.touches || e.touches.length === 0) return;

		const touch = e.touches[0];
		const moveX = touch.clientX - touchData.lastX;
		const moveY = touch.clientY - touchData.lastY;

		// 旋转3D模型
		if (threeData.model) {
			threeData.model.rotation.y += moveX * 0.01;
			threeData.model.rotation.x += moveY * 0.01;
		}

		touchData.lastX = touch.clientX;
		touchData.lastY = touch.clientY;

		console.log('3D模型旋转中...', { moveX, moveY });
	};

	const onTouchEnd = (e) => {
		console.log('3D模型触摸结束', e);
		touchData.isRotating = false;
	};

	// 清理Three.js资源 (按照文档方式)
	const cleanup = () => {
		if (threeData.animationId) {
			cancelAnimationFrame(threeData.animationId);
			threeData.animationId = null;
		}
		if (threeData.renderer) {
			threeData.renderer.dispose();
			threeData.renderer = null;
		}
		if (threeData.scene) {
			threeData.scene.clear();
			threeData.scene = null;
		}
		if (threeData.mixer) {
			threeData.mixer = null;
		}
		console.log('Three.js资源已清理');
	};

	// 监听语言变化，更新下拉选项
	onMounted(async () => {
		console.log('UniApp页面挂载完成');

		// 监听语言变化
		uni.$on('language-changed', () => {
			// 更新下拉选项文本
			locationRange.value = [{
					value: 1,
					text: t('detailsPage.studentParent')
				},
				{
					value: 2,
					text: t('detailsPage.teacher')
				},
				{
					value: 3,
					text: t('detailsPage.student')
				}
			];
			sceneRange.value = [{
					value: 1,
					text: t('detailsPage.parentMeeting')
				},
				{
					value: 2,
					text: t('detailsPage.classroom')
				},
				{
					value: 3,
					text: t('detailsPage.teachingResearch')
				}
			];

			// 更新标题
			title.value = t('detailsPage.title');
		});
	});

	// 组件卸载时清理资源
	onUnmounted(() => {
		console.log('页面卸载，清理Three.js资源');
		cleanup();
	});
</script>

<style lang="scss" scoped>
	/* 3D数字人容器样式 */
	.digital-human-3d-container {
		position: relative;
		width: 100%;
		height: 33.33vh; /* 页面上1/3部分 */
		background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
		border-radius: 0 0 20rpx 20rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	}

	.three-canvas {
		width: 100%;
		height: 100%;
		display: block;
	}

	.webview-3d {
		width: 100%;
		height: 100%;
		border: none;
		background: transparent;
	}

	.loading-overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(245, 247, 250, 0.9);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 10;
	}

	.loading-spinner {
		width: 60rpx;
		height: 60rpx;
		border: 4rpx solid #e0e0e0;
		border-top: 4rpx solid #1677c7;
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	.loading-text {
		font-size: 32rpx;
		color: #006397;
		font-weight: 500;
	}

	.fallback-container {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	}

	.teacher-avatar {
		position: relative;
		margin-bottom: 20rpx;
	}

	.avatar-circle {
		width: 160rpx;
		height: 160rpx;
		border-radius: 50%;
		background: linear-gradient(135deg, #4a90e2 0%, #006397 100%);
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 8rpx 24rpx rgba(0, 99, 151, 0.3);
		position: relative;
		z-index: 2;
	}

	.avatar-emoji {
		font-size: 80rpx;
		line-height: 1;
	}

	.avatar-glow {
		position: absolute;
		top: -10rpx;
		left: -10rpx;
		width: 180rpx;
		height: 180rpx;
		border-radius: 50%;
		background: linear-gradient(135deg, #4a90e2 0%, #006397 100%);
		opacity: 0.3;
		animation: pulse 2s infinite;
		z-index: 1;
	}

	.teacher-info {
		text-align: center;
	}

	.teacher-name {
		display: block;
		font-size: 32rpx;
		font-weight: 600;
		color: #006397;
		margin-bottom: 8rpx;
	}

	.teacher-subtitle {
		display: block;
		font-size: 24rpx;
		color: #666;
		opacity: 0.8;
	}

	.avatar-animation {
		position: absolute;
		width: 240rpx;
		height: 240rpx;
		border: 2rpx solid rgba(0, 99, 151, 0.3);
		border-radius: 50%;
		animation: pulse-ring 2s ease-in-out infinite;
	}

	/* 数字人背景图像样式 */
	.digital-human-background {
		position: fixed;
		left: 0;
		top: 0;
		width: 100vw;
		height: 100vh;
		z-index: -1;
		/* 确保在页面内容之下，但仍然可见 */
		pointer-events: none;
		/* 确保点击事件能穿透到前面的内容 */
		overflow: hidden;
		display: flex;
		justify-content: center;
		align-items: center;
		background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
		/* 渐变背景 */
	}

	.digital-human-webview {
		width: 100%;
		height: 100%;
		border: none;
		background: transparent;
	}

	.digital-human-image {
		width: auto;
		height: 100vh;
		max-width: 100vw;
		object-fit: contain;
		opacity: 0.8;
		/* 稍微透明，让前景内容更清晰 */
		animation: float 6s ease-in-out infinite;
		/* 添加浮动动画 */
	}

	@keyframes breathe {

		0%,
		100% {
			transform: scale(1);
		}

		50% {
			transform: scale(1.08);
		}
	}

	@keyframes float {

		0%,
		100% {
			transform: translateY(0px);
		}

		50% {
			transform: translateY(-15px);
		}
	}

	@keyframes rotate {
		0% {
			transform: rotate(0deg);
		}

		100% {
			transform: rotate(360deg);
		}
	}

	@keyframes pulse-ring {

		0%,
		100% {
			opacity: 0.3;
			transform: scale(1);
		}

		50% {
			opacity: 0.8;
			transform: scale(1.1);
		}
	}

	@keyframes pulse-glow {

		0%,
		100% {
			opacity: 0.2;
			transform: scale(1);
		}

		50% {
			opacity: 0.6;
			transform: scale(1.2);
		}
	}

	@keyframes orbit {
		0% {
			opacity: 0;
			transform: rotate(var(--angle)) translateX(150rpx) scale(0);
		}

		25% {
			opacity: 1;
			transform: rotate(var(--angle)) translateX(200rpx) scale(1);
		}

		75% {
			opacity: 1;
			transform: rotate(var(--angle)) translateX(200rpx) scale(1);
		}

		100% {
			opacity: 0;
			transform: rotate(var(--angle)) translateX(150rpx) scale(0);
		}
	}

	/* 确保页面内容正确布局 */
	.page-details {
		position: relative;
		min-height: 100vh;
		display: flex;
		flex-direction: column;
		background: #ffffff;
	}

	.content {
		flex: 1;
		padding: 20rpx 30rpx;
		background: #ffffff;
	}

	.options-row {
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 20rpx;
	}

	.option-item {
		display: flex;
		align-items: center;
		border-radius: 40rpx;
		border: 2px solid #F1F4F9;
		width: 146rpx;
		height: 60rpx;
	}

	.icon {
		width: 38rpx;
		height: 38rpx;
		margin-left: 8rpx;
	}

	.label-text {
		font-size: 24rpx;
		color: #006397;
		margin-left: 8rpx;
		font-weight: 600;
	}

	.option-item-location {
		position: relative;
		display: flex;
		align-items: center;
		border-radius: 40rpx;
		border: 2px solid #F1F4F9;
		width: 220rpx;
		height: 60rpx;
	}

	.label {
		font-size: 28rpx;
		color: #006397;
		margin-left: 8rpx;
	}

	.select-box {
		flex: 1;
	}

	:deep(.uni-data-select) {
		margin: 0;
		padding-right: 20rpx;
	}

	:deep(.uni-select__input-box) {
		height: 72rpx;
		border: none !important;
		padding: 0 !important;
	}

	:deep(.uni-select__input-text) {
		font-size: 20rpx;
		color: #006397;
	}

	:deep(.uni-select__selector) {
		border-radius: 16rpx;
		border: none;
	}

	:deep(.uni-select__selector-item) {
		font-size: 20rpx;
		padding: 20rpx;
		color: #006397;
	}

	.arrow-right {
		font-weight: 600;
	}

	/* 隐藏默认的下拉箭头 */
	:deep(.uni-select__input-box::after) {
		display: none;
	}

	/* 自定义下拉箭头位置 */
	.option-item-location .uni-icons {
		position: absolute;
		right: 20rpx;
		pointer-events: none;
	}

	.Plate {
		margin-top: 20rpx;
		line-height: 45rpx;
		color: #006397;
		font-size: 24rpx;
	}

	.Plate-nav {
		display: flex;
	}

	.Plate-name {
		font-weight: 600;
		margin-left: 4rpx;
	}

	.Training-nav {
		display: flex;
		align-items: center;
		margin-top: 20rpx;
	}

	.Training-icon {
		width: 56rpx;
		height: 24rpx;
	}

	.Training {
		font-size: 24rpx;
		color: #000000;
		font-weight: 600;
	}

	.focus-line {
		width: 240rpx;
		height: 6rpx;
		border-radius: 1.5px;
		background: linear-gradient(.25turn, #006397, 50%, #F1F4F9);
		margin-bottom: 20rpx;
	}

	.avatar-section {
		display: flex;
		justify-content: center;
		align-items: center;
		margin: 20rpx 0;
	}

	.avatar-container {
		position: relative;
		background: rgba(255, 255, 255, 0.8);
		border-radius: 50%;
		padding: 10rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
		backdrop-filter: blur(5px);
	}

	.avatar-image {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		display: block;
	}

	.zoom-icon {
		position: absolute;
		top: 25rpx;
		right: 26rpx;
		width: 32rpx;
		height: 32rpx;
	}

	.chat-container {
		flex: 1;
		margin: 20rpx 0;
		height: 500rpx; /* 调整高度适应新布局 */
		background: rgba(248, 249, 250, 0.95);
		border-radius: 20rpx;
		padding: 20rpx;
		position: relative;
		overflow: hidden;
		border: 1px solid rgba(0, 99, 151, 0.1);
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	}

	.chat-messages {
		height: 100%;
		-webkit-overflow-scrolling: touch;
	}

	.bubble-row {
		display: flex;
		align-items: flex-start;
		margin-bottom: 24rpx;
	}

	.lang-label {
		width: 56rpx;
		height: 56rpx;
		margin-right: 12rpx;
	}

	.avatar-bubble {
		width: 64rpx;
		height: 64rpx;
		border-radius: 50%;
		margin-right: 16rpx;
	}

	.bubble-cn {
		background: rgba(230, 243, 255, 0.6);
		/* 半透明蓝色 */
		border-radius: 16rpx;
		padding: 24rpx 30rpx;
		max-width: 80%;
		margin-bottom: 16rpx;
		box-shadow: none;
	}

	.bubble-cn-text {
		font-size: 32rpx;
		color: #333;
		font-weight: normal;
		line-height: 1.5;
	}

	.message-icon-container {
		width: 48rpx;
		height: 48rpx;
		margin-right: 12rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.message-icon {
		width: 40rpx;
		height: 40rpx;
	}

	.bubble-uy {
		background: #F6F7F9;
		border-radius: 32rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
		padding: 32rpx 36rpx 24rpx 36rpx;
		max-width: 80%;
		margin-top: 8rpx;
		margin-bottom: 16rpx;
	}

	.bubble-uy-text {
		font-size: 32rpx;
		color: #222;
		font-weight: 500;
		line-height: 1.7;
		word-break: break-all;
		display: inline-block;
	}

	.bottom-bar {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(255, 255, 255, 0.95);
		backdrop-filter: blur(10px);
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
		padding-bottom: env(safe-area-inset-bottom);
		z-index: 10;
		border-top: 1px solid rgba(0, 99, 151, 0.1);
	}

	.input-wrapper {
		display: flex;
		align-items: center;
		padding: 16rpx 24rpx;
		gap: 16rpx;
	}

	.mode-switch {
		width: 64rpx;
		height: 64rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background: transparent;
		padding: 10rpx;
	}

	.mode-icon {
		width: 44rpx;
		height: 44rpx;
		display: block;
		filter: brightness(0);
		/* 将白色图标转换为黑色 */
	}

	.voice-btn {
		flex: 1;
		height: 64rpx;
		background: #FFFFFF;
		border-radius: 8rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.voice-btn-text {
		font-size: 28rpx;
		color: #333;
	}

	.input-box {
		flex: 1;
		background: #FFFFFF;
		border-radius: 8rpx;
		padding: 16rpx;
		height: 64rpx;
		display: flex;
		align-items: center;
	}

	.message-input {
		width: 100%;
		height: 100%;
		font-size: 28rpx;
		background: transparent;
		border: none;
	}

	.send-btn {
		min-width: 100rpx;
		height: 64rpx;
		background: #07C160;
		color: #FFFFFF;
		border-radius: 8rpx;
		font-size: 28rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border: none;
		padding: 0 24rpx;
	}

	.user-row {
		flex-direction: row-reverse;
	}

	.avatar-user {
		width: 64rpx;
		height: 64rpx;
		border-radius: 50%;
		margin-left: 16rpx;
	}

	.user-bubble {
		background: rgba(200, 249, 140, 0.5);
		/* 半透明绿色 */
		color: #222;
		margin-left: 16rpx;
	}

	.pinyin-row-align {
		display: flex;
		justify-content: center;
		margin-top: 8rpx;
		gap: 12rpx;
	}

	.pinyin-word {
		font-size: 20rpx;
		color: #A0A0A0;
		letter-spacing: 1rpx;
		text-align: center;
	}

	.zh-row-align {
		display: flex;
		justify-content: center;
		margin-bottom: 12rpx;
		gap: 12rpx;
	}

	.zh-word {
		font-size: 36rpx;
		color: #1677c7;
		font-weight: bold;
		letter-spacing: 1rpx;
		text-align: center;
	}

	.uy-header {
		display: flex;
		align-items: flex-start;
		justify-content: space-between;
	}

	.uy-speaker {
		width: 36rpx;
		height: 36rpx;
		margin-left: 12rpx;
		margin-top: 4rpx;
	}

	.voice-message {
		display: flex;
		align-items: center;
		gap: 8rpx;
		padding: 8rpx 16rpx;
		background: rgba(0, 0, 0, 0.05);
		border-radius: 32rpx;
	}

	.voice-text {
		font-size: 28rpx;
		color: #222;
		margin-left: 4rpx;
	}

	.user-bubble .voice-message {
		background: rgba(200, 249, 140, 0.3);
	}

	.bubble-cn .voice-message {
		background: rgba(230, 243, 255, 0.5);
	}

	.unread-dot {
		width: 12rpx;
		height: 12rpx;
		border-radius: 50%;
		background-color: #FF0000;
		margin-left: 8rpx;
	}

	.category-buttons {
		display: flex;
		white-space: nowrap;
		// padding: 20rpx 0;
		// padding-bottom: 40rpx;
		// margin-top: -50rpx;
	}

	.category-button {
		display: inline-block;
		padding: 14rpx 30rpx;
		border-radius: 100rpx;
		font-size: 28rpx;
		color: #333333;
		transition: all 0.3s ease;
		text-align: center;
		margin-right: 20rpx;
		min-width: 120rpx;
		border: 1px solid #CCCCCC;
		background-color: #FFFFFF;
	}

	.category-button:first-child {
		margin-left: 20rpx;
	}

	.category-button.active {
		color: #000000;
		font-weight: 600;
		border-color: #000000;
	}

	@font-face {
		font-family: 'UKIJTor';
		src: url('/static/font/UKIJTor.ttf') format('truetype');
		font-weight: normal;
		font-style: normal;
	}

	.uyghur-text {
		font-family: 'UKIJTor', sans-serif;
	}

	/* 录音浮层相关样式 */
	.recording-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.3);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 999;
	}

	.recording-box {
		width: 300rpx;
		height: 300rpx;
		background: rgba(255, 255, 255, 0.95);
		border-radius: 24rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
	}

	.recording-box.cancel-recording {
		background: #FFE2E2;
	}

	.recording-icon-wrapper {
		width: 120rpx;
		height: 120rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 20rpx;
		position: relative;
	}

	.recording-waves {
		width: 100%;
		height: 100%;
		position: relative;
	}

	.wave {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		border: 4rpx solid #07C160;
		border-radius: 50%;
		opacity: 1;
		animation: wave-animation 1.5s infinite ease-out;
	}

	@keyframes wave-animation {
		0% {
			transform: scale(0.5);
			opacity: 1;
		}

		100% {
			transform: scale(1.5);
			opacity: 0;
		}
	}

	.cancel-icon {
		width: 80rpx;
		height: 80rpx;
		filter: invert(23%) sepia(93%) saturate(6977%) hue-rotate(358deg) brightness(101%) contrast(122%);
	}

	.recording-status {
		font-size: 32rpx;
		color: #333;
		margin-bottom: 16rpx;
		font-weight: 500;
	}

	.recording-tip {
		font-size: 24rpx;
		color: #999;
	}
</style>