{"version": 3, "file": "settings.js", "sources": ["sub-pack-2/particles/core/settings.ts"], "sourcesContent": ["import * as THREE from 'three';\r\nconst amountMap = {\r\n  '4k': [64, 64, 0.29],\r\n  '8k': [128, 64, 0.42],\r\n  '16k': [128, 128, 0.48],\r\n  '32k': [256, 128, 0.55],\r\n  '65k': [256, 256, 0.6],\r\n  '131k': [512, 256, 0.85],\r\n  '252k': [512, 512, 1.2],\r\n  '524k': [1024, 512, 1.4],\r\n  '1m': [1024, 1024, 1.6],\r\n  '2m': [2048, 1024, 2],\r\n  '4m': [2048, 2048, 2.5]\r\n};\r\n\r\nconst motionBlurQualityMap = {\r\n  best: 1,\r\n  high: 0.5,\r\n  medium: 1 / 3,\r\n  low: 0.25\r\n};\r\nconst query = {\r\n  motionBlurQuality: 'medium',\r\n  amount: '131k' as keyof typeof amountMap\r\n};\r\n\r\nconst amountInfo = amountMap[query.amount];\r\n\r\nexport const ray = new THREE.Ray();\r\n\r\nexport const settings = {\r\n  query,\r\n  useStats: true,\r\n  isMobile: true,\r\n  amountList: Object.keys(amountMap),\r\n  simulatorTextureWidth: amountInfo[0],\r\n  simulatorTextureHeight: amountInfo[1],\r\n  useTriangleParticles: true,\r\n  followMouse: true,\r\n  speed: 1,\r\n  dieSpeed: 0.015,\r\n  radius: amountInfo[2] / 2,\r\n  curlSize: 0.02,\r\n  attraction: 1.1,\r\n  shadowDarkness: 0,\r\n  bgColor: '#343434',\r\n  color1: '#ffffff',\r\n  color2: '#ffffff',\r\n  fxaa: false,\r\n  motionBlurQualityMap,\r\n  motionBlurQualityList: Object.keys(motionBlurQualityMap),\r\n\r\n  motionBlur: true,\r\n  motionBlurPause: false,\r\n  bloom: true,\r\n\r\n  mouse: new THREE.Vector2(0, 0),\r\n  mouse3d: ray.origin,\r\n  camera: null as unknown as THREE.Camera,\r\n  cameraPosition: null as unknown as THREE.Vector3\r\n};\r\n"], "names": ["THREE.Ray", "THREE.Vector2"], "mappings": ";;;AACA,MAAM,YAAY;AAAA,EAChB,MAAM,CAAC,IAAI,IAAI,IAAI;AAAA,EACnB,MAAM,CAAC,KAAK,IAAI,IAAI;AAAA,EACpB,OAAO,CAAC,KAAK,KAAK,IAAI;AAAA,EACtB,OAAO,CAAC,KAAK,KAAK,IAAI;AAAA,EACtB,OAAO,CAAC,KAAK,KAAK,GAAG;AAAA,EACrB,QAAQ,CAAC,KAAK,KAAK,IAAI;AAAA,EACvB,QAAQ,CAAC,KAAK,KAAK,GAAG;AAAA,EACtB,QAAQ,CAAC,MAAM,KAAK,GAAG;AAAA,EACvB,MAAM,CAAC,MAAM,MAAM,GAAG;AAAA,EACtB,MAAM,CAAC,MAAM,MAAM,CAAC;AAAA,EACpB,MAAM,CAAC,MAAM,MAAM,GAAG;AACxB;AAQA,MAAM,QAAQ;AAAA,EAEZ,QAAQ;AACV;AAEA,MAAM,aAAa,UAAU,MAAM,MAAM;AAElC,MAAM,MAAM,IAAIA,wCAAAA,GAAM;AAEtB,MAAM,WAAW;AAAA,EAKtB,uBAAuB,WAAW,CAAC;AAAA,EACnC,wBAAwB,WAAW,CAAC;AAAA,EACpC,sBAAsB;AAAA,EACtB,aAAa;AAAA,EACb,OAAO;AAAA,EACP,UAAU;AAAA,EACV,QAAQ,WAAW,CAAC,IAAI;AAAA,EACxB,UAAU;AAAA,EACV,YAAY;AAAA,EAEZ,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AAAA,EASR,OAAO,IAAIC,wCAAAA,GAAc,GAAG,CAAC;AAAA,EAC7B,SAAS,IAAI;AAAA,EACb,QAAQ;AAAA,EACR,gBAAgB;AAClB;;;"}