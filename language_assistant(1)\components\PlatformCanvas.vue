<template>
  <!-- 微信小程序环境 -->
  <!-- #ifdef MP-WEIXIN -->
  <canvas
    class="platform-canvas"
    :type="type"
    :canvas-id="canvasId"
    :id="canvasId"
    @touchstart="handleTouch"
    @touchmove="handleTouch"
    @touchcancel="handleTouch"
    @touchend="handleTouch"
    @tap="handleTouch"
    disable-scroll
  >
  </canvas>
  <!-- #endif -->
  
  <!-- H5环境 -->
  <!-- #ifdef H5 -->
  <canvas
    class="platform-canvas"
    :id="canvasId"
    @touchstart.stop="handleTouch"
    @touchmove.stop="handleTouch"
    @touchcancel.stop="handleTouch"
    @touchend.stop="handleTouch"
    @click.stop="handleTouch"
  />
  <!-- #endif -->
  
  <!-- 其他平台 -->
  <!-- #ifndef MP-WEIXIN || H5 -->
  <canvas
    class="platform-canvas"
    :type="type"
    :canvas-id="canvasId"
    :id="canvasId"
    @touchstart="handleTouch"
    @touchmove="handleTouch"
    @touchcancel="handleTouch"
    @touchend="handleTouch"
    @tap="handleTouch"
    disable-scroll
  >
  </canvas>
  <!-- #endif -->
</template>

<script setup>
import { getCurrentInstance, onMounted, nextTick } from 'vue'

const props = defineProps({
  type: {
    type: String,
    default: 'webgl'
  },
  canvasId: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['useCanvas', 'touchstart', 'touchmove', 'touchend', 'touchcancel', 'tap'])

const handleTouch = (e) => {
  // 处理点击事件转换为tap事件
  if (e.type === 'click') {
    e = {
      ...e,
      type: 'tap'
    }
  }
  emit(e.type, e)
}

const instance = getCurrentInstance()

onMounted(async () => {
  await nextTick()

  // 延迟获取canvas，确保DOM渲染完成
  setTimeout(() => {
    getCanvasNode()
  }, 500) // 增加延迟时间，确保DOM完全渲染
})

const getCanvasNode = () => {
  const query = uni.createSelectorQuery().in(instance)

  query.select(`#${props.canvasId}`)
    .node()
    .exec((res) => {
      if (res && res[0] && res[0].node) {
        const canvas = res[0].node
        console.log('Canvas节点获取成功:', canvas)

        // 设置canvas尺寸
        const dpr = uni.getSystemInfoSync().pixelRatio || 1

        // 获取canvas的显示尺寸
        query.select(`#${props.canvasId}`)
          .boundingClientRect()
          .exec((rectRes) => {
            console.log('Canvas boundingClientRect结果:', rectRes)

            let width, height

            if (rectRes && rectRes[0] && rectRes[0].width && rectRes[0].height) {
              width = rectRes[0].width
              height = rectRes[0].height
            } else {
              // 如果无法获取尺寸，使用系统信息计算默认尺寸
              const systemInfo = uni.getSystemInfoSync()
              width = systemInfo.windowWidth || 375
              height = Math.floor(systemInfo.windowHeight * 0.33) || 250 // 页面上三分之一
              console.log('使用默认尺寸，基于系统信息:', { width, height, systemInfo })
            }

            // 设置canvas的实际尺寸
            canvas.width = width * dpr
            canvas.height = height * dpr

            console.log('微信小程序Canvas尺寸设置:', {
              displayWidth: width,
              displayHeight: height,
              actualWidth: canvas.width,
              actualHeight: canvas.height,
              dpr,
              platform: '微信小程序'
            })

            // 创建useFrame函数
            const useFrame = (callback) => {
              const animate = () => {
                callback()
                requestAnimationFrame(animate)
              }
              animate()
            }

            // 创建recomputeSize函数
            const recomputeSize = () => {
              return new Promise((resolve) => {
                query.select(`#${props.canvasId}`)
                  .boundingClientRect()
                  .exec((newRectRes) => {
                    if (newRectRes && newRectRes[0]) {
                      const { width: newWidth, height: newHeight } = newRectRes[0]
                      canvas.width = newWidth * dpr
                      canvas.height = newHeight * dpr
                      console.log('Canvas尺寸重新计算:', { width: newWidth, height: newHeight })
                      resolve({ width: newWidth, height: newHeight })
                    }
                  })
              })
            }

            // 发射useCanvas事件，传递canvas节点和辅助函数
            emit('useCanvas', {
              canvas,
              useFrame,
              recomputeSize,
              width: canvas.width,
              height: canvas.height
            })
          })
      } else {
        console.error('无法获取Canvas节点，重试中...')
        // 重试机制
        setTimeout(() => {
          getCanvasNode()
        }, 500)
      }
    })
}
</script>

<style lang="scss" scoped>
.platform-canvas {
  width: 100%;
  height: 100%;
  display: block;
}
</style>
