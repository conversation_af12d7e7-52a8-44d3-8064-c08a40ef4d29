{"version": 3, "file": "index.esm.js", "sources": ["../node_modules/@dcloudio/uni-console/dist/index.esm.js"], "sourcesContent": ["/// <reference types=\"@dcloudio/uni-app-x/types/uni/global\" />\nfunction initRuntimeSocket(hosts, port, id) {\n    if (hosts == '' || port == '' || id == '')\n        return Promise.resolve(null);\n    return hosts\n        .split(',')\n        .reduce((promise, host) => {\n        return promise.then((socket) => {\n            if (socket != null)\n                return Promise.resolve(socket);\n            return tryConnectSocket(host, port, id);\n        });\n    }, Promise.resolve(null));\n}\nconst SOCKET_TIMEOUT = 500;\nfunction tryConnectSocket(host, port, id) {\n    return new Promise((resolve, reject) => {\n        const socket = uni.connectSocket({\n            url: `ws://${host}:${port}/${id}`,\n            multiple: true, // 支付宝小程序 是否开启多实例\n            fail() {\n                resolve(null);\n            },\n        });\n        const timer = setTimeout(() => {\n            // @ts-expect-error\n            socket.close({\n                code: 1006,\n                reason: 'connect timeout',\n            });\n            resolve(null);\n        }, SOCKET_TIMEOUT);\n        socket.onOpen((e) => {\n            clearTimeout(timer);\n            resolve(socket);\n        });\n        socket.onClose((e) => {\n            clearTimeout(timer);\n            resolve(null);\n        });\n        socket.onError((e) => {\n            clearTimeout(timer);\n            resolve(null);\n        });\n    });\n}\n\nfunction formatMessage(type, args) {\n    try {\n        return {\n            type,\n            args: formatArgs(args),\n        };\n    }\n    catch (e) {\n        // originalConsole.error(e)\n    }\n    return {\n        type,\n        args: [],\n    };\n}\nfunction formatArgs(args) {\n    return args.map((arg) => formatArg(arg));\n}\nfunction formatArg(arg, depth = 0) {\n    if (depth >= 7) {\n        return {\n            type: 'object',\n            value: '[Maximum depth reached]',\n        };\n    }\n    const type = typeof arg;\n    switch (type) {\n        case 'string':\n            return formatString(arg);\n        case 'number':\n            return formatNumber(arg);\n        case 'boolean':\n            return formatBoolean(arg);\n        case 'object':\n            return formatObject(arg, depth);\n        case 'undefined':\n            return formatUndefined();\n        case 'function':\n            return formatFunction(arg);\n        case 'symbol':\n            {\n                return formatSymbol(arg);\n            }\n        case 'bigint':\n            return formatBigInt(arg);\n    }\n}\nfunction formatFunction(value) {\n    return {\n        type: 'function',\n        value: `function ${value.name}() {}`,\n    };\n}\nfunction formatUndefined() {\n    return {\n        type: 'undefined',\n    };\n}\nfunction formatBoolean(value) {\n    return {\n        type: 'boolean',\n        value: String(value),\n    };\n}\nfunction formatNumber(value) {\n    return {\n        type: 'number',\n        value: String(value),\n    };\n}\nfunction formatBigInt(value) {\n    return {\n        type: 'bigint',\n        value: String(value),\n    };\n}\nfunction formatString(value) {\n    return {\n        type: 'string',\n        value,\n    };\n}\nfunction formatSymbol(value) {\n    return {\n        type: 'symbol',\n        value: value.description,\n    };\n}\nfunction formatObject(value, depth) {\n    if (value === null) {\n        return {\n            type: 'null',\n        };\n    }\n    {\n        if (isComponentPublicInstance(value)) {\n            return formatComponentPublicInstance(value, depth);\n        }\n        if (isComponentInternalInstance(value)) {\n            return formatComponentInternalInstance(value, depth);\n        }\n        if (isUniElement(value)) {\n            return formatUniElement(value, depth);\n        }\n        if (isCSSStyleDeclaration(value)) {\n            return formatCSSStyleDeclaration(value, depth);\n        }\n    }\n    if (Array.isArray(value)) {\n        return {\n            type: 'object',\n            subType: 'array',\n            value: {\n                properties: value.map((v, i) => formatArrayElement(v, i, depth + 1)),\n            },\n        };\n    }\n    if (value instanceof Set) {\n        return {\n            type: 'object',\n            subType: 'set',\n            className: 'Set',\n            description: `Set(${value.size})`,\n            value: {\n                entries: Array.from(value).map((v) => formatSetEntry(v, depth + 1)),\n            },\n        };\n    }\n    if (value instanceof Map) {\n        return {\n            type: 'object',\n            subType: 'map',\n            className: 'Map',\n            description: `Map(${value.size})`,\n            value: {\n                entries: Array.from(value.entries()).map((v) => formatMapEntry(v, depth + 1)),\n            },\n        };\n    }\n    if (value instanceof Promise) {\n        return {\n            type: 'object',\n            subType: 'promise',\n            value: {\n                properties: [],\n            },\n        };\n    }\n    if (value instanceof RegExp) {\n        return {\n            type: 'object',\n            subType: 'regexp',\n            value: String(value),\n            className: 'Regexp',\n        };\n    }\n    if (value instanceof Date) {\n        return {\n            type: 'object',\n            subType: 'date',\n            value: String(value),\n            className: 'Date',\n        };\n    }\n    if (value instanceof Error) {\n        return {\n            type: 'object',\n            subType: 'error',\n            value: value.message || String(value),\n            className: value.name || 'Error',\n        };\n    }\n    let className = undefined;\n    {\n        const constructor = value.constructor;\n        if (constructor) {\n            // @ts-expect-error\n            if (constructor.get$UTSMetadata$) {\n                // @ts-expect-error\n                className = constructor.get$UTSMetadata$().name;\n            }\n        }\n    }\n    return {\n        type: 'object',\n        className,\n        value: {\n            properties: Object.entries(value).map((entry) => formatObjectProperty(entry[0], entry[1], depth + 1)),\n        },\n    };\n}\nfunction isComponentPublicInstance(value) {\n    return value.$ && isComponentInternalInstance(value.$);\n}\nfunction isComponentInternalInstance(value) {\n    return value.type && value.uid != null && value.appContext;\n}\nfunction formatComponentPublicInstance(value, depth) {\n    return {\n        type: 'object',\n        className: 'ComponentPublicInstance',\n        value: {\n            properties: Object.entries(value.$.type).map(([name, value]) => formatObjectProperty(name, value, depth + 1)),\n        },\n    };\n}\nfunction formatComponentInternalInstance(value, depth) {\n    return {\n        type: 'object',\n        className: 'ComponentInternalInstance',\n        value: {\n            properties: Object.entries(value.type).map(([name, value]) => formatObjectProperty(name, value, depth + 1)),\n        },\n    };\n}\nfunction isUniElement(value) {\n    return value.style && value.tagName != null && value.nodeName != null;\n}\nfunction formatUniElement(value, depth) {\n    return {\n        type: 'object',\n        // 非 x 没有 UniElement 的概念\n        // className: 'UniElement',\n        value: {\n            properties: Object.entries(value)\n                .filter(([name]) => [\n                'id',\n                'tagName',\n                'nodeName',\n                'dataset',\n                'offsetTop',\n                'offsetLeft',\n                'style',\n            ].includes(name))\n                .map(([name, value]) => formatObjectProperty(name, value, depth + 1)),\n        },\n    };\n}\nfunction isCSSStyleDeclaration(value) {\n    return (typeof value.getPropertyValue === 'function' &&\n        typeof value.setProperty === 'function' &&\n        value.$styles);\n}\nfunction formatCSSStyleDeclaration(style, depth) {\n    return {\n        type: 'object',\n        value: {\n            properties: Object.entries(style.$styles).map(([name, value]) => formatObjectProperty(name, value, depth + 1)),\n        },\n    };\n}\nfunction formatObjectProperty(name, value, depth) {\n    const result = formatArg(value, depth);\n    result.name = name;\n    return result;\n}\nfunction formatArrayElement(value, index, depth) {\n    const result = formatArg(value, depth);\n    result.name = `${index}`;\n    return result;\n}\nfunction formatSetEntry(value, depth) {\n    return {\n        value: formatArg(value, depth),\n    };\n}\nfunction formatMapEntry(value, depth) {\n    return {\n        key: formatArg(value[0], depth),\n        value: formatArg(value[1], depth),\n    };\n}\n\nconst CONSOLE_TYPES = ['log', 'warn', 'error', 'info', 'debug'];\nlet sendConsole = null;\nconst messageQueue = [];\nconst messageExtra = {};\nfunction sendConsoleMessages(messages) {\n    if (sendConsole == null) {\n        messageQueue.push(...messages);\n        return;\n    }\n    sendConsole(JSON.stringify(Object.assign({\n        type: 'console',\n        data: messages,\n    }, messageExtra)));\n}\nfunction setSendConsole(value, extra = {}) {\n    sendConsole = value;\n    Object.assign(messageExtra, extra);\n    if (value != null && messageQueue.length > 0) {\n        const messages = messageQueue.slice();\n        messageQueue.length = 0;\n        sendConsoleMessages(messages);\n    }\n}\nconst originalConsole = /*@__PURE__*/ CONSOLE_TYPES.reduce((methods, type) => {\n    methods[type] = console[type].bind(console);\n    return methods;\n}, {});\nconst atFileRegex = /^\\s*at\\s+[\\w/./-]+:\\d+$/;\nfunction rewriteConsole() {\n    function wrapConsole(type) {\n        return function (...args) {\n            const originalArgs = [...args];\n            if (originalArgs.length) {\n                const maybeAtFile = originalArgs[originalArgs.length - 1];\n                // 移除最后的 at pages/index/index.uvue:6\n                if (typeof maybeAtFile === 'string' && atFileRegex.test(maybeAtFile)) {\n                    originalArgs.pop();\n                }\n            }\n            if (process.env.UNI_CONSOLE_KEEP_ORIGINAL) {\n                originalConsole[type](...originalArgs);\n            }\n            sendConsoleMessages([formatMessage(type, args)]);\n        };\n    }\n    // 百度小程序不允许赋值，所以需要判断是否可写\n    if (isConsoleWritable()) {\n        CONSOLE_TYPES.forEach((type) => {\n            console[type] = wrapConsole(type);\n        });\n        return function restoreConsole() {\n            CONSOLE_TYPES.forEach((type) => {\n                console[type] = originalConsole[type];\n            });\n        };\n    }\n    else {\n        {\n            if (typeof uni !== 'undefined' && uni.__f__) {\n                const oldLog = uni.__f__;\n                if (oldLog) {\n                    // 重写 uni.__f__ 方法，这样的话，仅能打印开发者代码里的日志，其他没有被重写为__f__的日志将无法打印（比如uni-app框架、小程序框架等）\n                    uni.__f__ = function (...args) {\n                        const [type, filename, ...rest] = args;\n                        // 原始日志移除 filename\n                        oldLog(type, '', ...rest);\n                        sendConsoleMessages([formatMessage(type, [...rest, filename])]);\n                    };\n                    return function restoreConsole() {\n                        uni.__f__ = oldLog;\n                    };\n                }\n            }\n        }\n    }\n    return function restoreConsole() { };\n}\nfunction isConsoleWritable() {\n    const value = console.log;\n    const sym = Symbol();\n    try {\n        // @ts-expect-error\n        console.log = sym;\n    }\n    catch (ex) {\n        return false;\n    }\n    // @ts-expect-error\n    const isWritable = console.log === sym;\n    console.log = value;\n    return isWritable;\n}\n\nlet sendError = null;\n// App.onError会监听到两类错误，一类是小程序自身抛出的，一类是 vue 的 errorHandler 触发的\n// uni.onError 和 App.onError 会同时监听到错误(主要是App.onError监听之前的错误)，所以需要用 Set 来去重\n// uni.onError 会在 App.onError 上边同时增加监听，因为要监听 vue 的errorHandler\n// 目前 vue 的 errorHandler 仅会callHook('onError')，所以需要把uni.onError的也挂在 App.onError 上\nconst errorQueue = new Set();\nconst errorExtra = {};\nfunction sendErrorMessages(errors) {\n    if (sendError == null) {\n        errors.forEach((error) => {\n            errorQueue.add(error);\n        });\n        return;\n    }\n    const data = errors\n        .map((err) => {\n        const isPromiseRejection = err && 'promise' in err && 'reason' in err;\n        const prefix = isPromiseRejection ? 'UnhandledPromiseRejection: ' : '';\n        if (isPromiseRejection) {\n            err = err.reason;\n        }\n        if (err instanceof Error && err.stack) {\n            if (err.message && !err.stack.includes(err.message)) {\n                return `${prefix}${err.message}\n${err.stack}`;\n            }\n            return `${prefix}${err.stack}`;\n        }\n        if (typeof err === 'object' && err !== null) {\n            try {\n                return prefix + JSON.stringify(err);\n            }\n            catch (err) {\n                return prefix + String(err);\n            }\n        }\n        return prefix + String(err);\n    })\n        .filter(Boolean);\n    if (data.length > 0) {\n        sendError(JSON.stringify(Object.assign({\n            type: 'error',\n            data,\n        }, errorExtra)));\n    }\n}\nfunction setSendError(value, extra = {}) {\n    sendError = value;\n    Object.assign(errorExtra, extra);\n    if (value != null && errorQueue.size > 0) {\n        const errors = Array.from(errorQueue);\n        errorQueue.clear();\n        sendErrorMessages(errors);\n    }\n}\nfunction initOnError() {\n    function onError(error) {\n        try {\n            // 小红书小程序 socket.send 时，会报错，onError错误信息为：\n            // Cannot create property 'errMsg' on string 'taskId'\n            // 导致陷入死循环\n            if (typeof PromiseRejectionEvent !== 'undefined' &&\n                error instanceof PromiseRejectionEvent &&\n                error.reason instanceof Error &&\n                error.reason.message &&\n                error.reason.message.includes(`Cannot create property 'errMsg' on string 'taskId`)) {\n                return;\n            }\n            if (process.env.UNI_CONSOLE_KEEP_ORIGINAL) {\n                originalConsole.error(error);\n            }\n            sendErrorMessages([error]);\n        }\n        catch (err) {\n            originalConsole.error(err);\n        }\n    }\n    if (typeof uni.onError === 'function') {\n        uni.onError(onError);\n    }\n    if (typeof uni.onUnhandledRejection === 'function') {\n        uni.onUnhandledRejection(onError);\n    }\n    return function offError() {\n        if (typeof uni.offError === 'function') {\n            uni.offError(onError);\n        }\n        if (typeof uni.offUnhandledRejection === 'function') {\n            uni.offUnhandledRejection(onError);\n        }\n    };\n}\n\nfunction initRuntimeSocketService() {\n    const hosts = process.env.UNI_SOCKET_HOSTS;\n    const port = process.env.UNI_SOCKET_PORT;\n    const id = process.env.UNI_SOCKET_ID;\n    if (!hosts || !port || !id)\n        return Promise.resolve(false);\n    // 百度小程序需要延迟初始化，不然会存在循环引用问题vendor.js\n    const lazy = typeof swan !== 'undefined';\n    // 重写需要同步，避免丢失早期日志信息\n    let restoreError = lazy ? () => { } : initOnError();\n    let restoreConsole = lazy ? () => { } : rewriteConsole();\n    // 百度小程序需要异步初始化，不然调用 uni.connectSocket 会循环引入vendor.js\n    return Promise.resolve().then(() => {\n        if (lazy) {\n            restoreError = initOnError();\n            restoreConsole = rewriteConsole();\n        }\n        return initRuntimeSocket(hosts, port, id).then((socket) => {\n            if (!socket) {\n                restoreError();\n                restoreConsole();\n                originalConsole.error(wrapError('开发模式下日志通道建立 socket 连接失败。'));\n                originalConsole.error(wrapError('如果是小程序平台，请勾选不校验合法域名配置。'));\n                originalConsole.error(wrapError('如果是运行到真机，请确认手机与电脑处于同一网络。'));\n                return false;\n            }\n            initMiniProgramGlobalFlag();\n            socket.onClose(() => {\n                if (process.env.UNI_DEBUG) {\n                    originalConsole.log(`uni-app:[${Date.now()}][socket]`, 'connect close and restore');\n                }\n                originalConsole.error(wrapError('开发模式下日志通道 socket 连接关闭，请在 HBuilderX 中重新运行。'));\n                restoreError();\n                restoreConsole();\n            });\n            setSendConsole((data) => {\n                if (process.env.UNI_DEBUG) {\n                    originalConsole.log(`uni-app:[${Date.now()}][console]`, data);\n                }\n                socket.send({\n                    data,\n                });\n            });\n            setSendError((data) => {\n                if (process.env.UNI_DEBUG) {\n                    originalConsole.log(`uni-app:[${Date.now()}][error]`, data);\n                }\n                socket.send({\n                    data,\n                });\n            });\n            return true;\n        });\n    });\n}\nconst ERROR_CHAR = '\\u200C';\nfunction wrapError(error) {\n    return `${ERROR_CHAR}${error}${ERROR_CHAR}`;\n}\nfunction initMiniProgramGlobalFlag() {\n    if (typeof wx !== 'undefined') {\n        // @ts-expect-error\n        wx.__uni_console__ = true;\n        // @ts-expect-error\n    }\n    else if (typeof my !== 'undefined') {\n        // @ts-expect-error\n        my.__uni_console__ = true;\n    }\n    else if (typeof tt !== 'undefined') {\n        tt.__uni_console__ = true;\n    }\n    else if (typeof swan !== 'undefined') {\n        swan.__uni_console__ = true;\n    }\n    else if (typeof qq !== 'undefined') {\n        qq.__uni_console__ = true;\n    }\n    else if (typeof ks !== 'undefined') {\n        ks.__uni_console__ = true;\n    }\n    else if (typeof jd !== 'undefined') {\n        jd.__uni_console__ = true;\n    }\n    else if (typeof xhs !== 'undefined') {\n        xhs.__uni_console__ = true;\n    }\n    else if (typeof has !== 'undefined') {\n        has.__uni_console__ = true;\n    }\n    else if (typeof qa !== 'undefined') {\n        qa.__uni_console__ = true;\n    }\n}\ninitRuntimeSocketService();\n\nexport { initRuntimeSocketService };\n"], "names": ["uni", "value", "err", "wx"], "mappings": ";;AACA,SAAS,kBAAkB,OAAO,MAAM,IAAI;AAGxC,SAAO,MACF,MAAM,GAAG,EACT,OAAO,CAAC,SAAS,SAAS;AAC3B,WAAO,QAAQ,KAAK,CAAC,WAAW;AAC5B,UAAI,UAAU;AACV,eAAO,QAAQ,QAAQ,MAAM;AACjC,aAAO,iBAAiB,MAAM,MAAM,EAAE;AAAA,IAC1C,CAAC;AAAA,EACL,GAAG,QAAQ,QAAQ,IAAI,CAAC;AAC5B;AACA,MAAM,iBAAiB;AACvB,SAAS,iBAAiB,MAAM,MAAM,IAAI;AACtC,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,UAAM,SAASA,oDAAAA,MAAI,cAAc;AAAA,MAC7B,KAAK,QAAQ,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MAC/B,UAAU;AAAA;AAAA,MACV,OAAO;AACH,gBAAQ,IAAI;AAAA,MAChB;AAAA,IAAA,CACH;AACD,UAAM,QAAQ,WAAW,MAAM;AAE3B,aAAO,MAAM;AAAA,QACT,MAAM;AAAA,QACN,QAAQ;AAAA,MAAA,CACX;AACD,cAAQ,IAAI;AAAA,IAChB,GAAG,cAAc;AACjB,WAAO,OAAO,CAAC,MAAM;AACjB,mCAAa,KAAK;AAClB,cAAQ,MAAM;AAAA,IAClB,CAAC;AACD,WAAO,QAAQ,CAAC,MAAM;AAClB,mCAAa,KAAK;AAClB,cAAQ,IAAI;AAAA,IAChB,CAAC;AACD,WAAO,QAAQ,CAAC,MAAM;AAClB,mCAAa,KAAK;AAClB,cAAQ,IAAI;AAAA,IAChB,CAAC;AAAA,EACL,CAAC;AACL;AAEA,SAAS,cAAc,MAAM,MAAM;AAC/B,MAAI;AACA,WAAO;AAAA,MACH;AAAA,MACA,MAAM,WAAW,IAAI;AAAA,IAAA;AAAA,EAE7B,SACO,GAAG;AAAA,EAEV;AACA,SAAO;AAAA,IACH;AAAA,IACA,MAAM,CAAA;AAAA,EAAC;AAEf;AACA,SAAS,WAAW,MAAM;AACtB,SAAO,KAAK,IAAI,CAAC,QAAQ,UAAU,GAAG,CAAC;AAC3C;AACA,SAAS,UAAU,KAAK,QAAQ,GAAG;AAC/B,MAAI,SAAS,GAAG;AACZ,WAAO;AAAA,MACH,MAAM;AAAA,MACN,OAAO;AAAA,IAAA;AAAA,EAEf;AACA,QAAM,OAAO,OAAO;AACpB,UAAQ,MAAA;AAAA,IACJ,KAAK;AACD,aAAO,aAAa,GAAG;AAAA,IAC3B,KAAK;AACD,aAAO,aAAa,GAAG;AAAA,IAC3B,KAAK;AACD,aAAO,cAAc,GAAG;AAAA,IAC5B,KAAK;AACD,aAAO,aAAa,KAAK,KAAK;AAAA,IAClC,KAAK;AACD,aAAO,gBAAA;AAAA,IACX,KAAK;AACD,aAAO,eAAe,GAAG;AAAA,IAC7B,KAAK,UACD;AACI,aAAO,aAAa,GAAG;AAAA,IAC3B;AAAA,IACJ,KAAK;AACD,aAAO,aAAa,GAAG;AAAA,EAAA;AAEnC;AACA,SAAS,eAAe,OAAO;AAC3B,SAAO;AAAA,IACH,MAAM;AAAA,IACN,OAAO,YAAY,MAAM,IAAI;AAAA,EAAA;AAErC;AACA,SAAS,kBAAkB;AACvB,SAAO;AAAA,IACH,MAAM;AAAA,EAAA;AAEd;AACA,SAAS,cAAc,OAAO;AAC1B,SAAO;AAAA,IACH,MAAM;AAAA,IACN,OAAO,OAAO,KAAK;AAAA,EAAA;AAE3B;AACA,SAAS,aAAa,OAAO;AACzB,SAAO;AAAA,IACH,MAAM;AAAA,IACN,OAAO,OAAO,KAAK;AAAA,EAAA;AAE3B;AACA,SAAS,aAAa,OAAO;AACzB,SAAO;AAAA,IACH,MAAM;AAAA,IACN,OAAO,OAAO,KAAK;AAAA,EAAA;AAE3B;AACA,SAAS,aAAa,OAAO;AACzB,SAAO;AAAA,IACH,MAAM;AAAA,IACN;AAAA,EAAA;AAER;AACA,SAAS,aAAa,OAAO;AACzB,SAAO;AAAA,IACH,MAAM;AAAA,IACN,OAAO,MAAM;AAAA,EAAA;AAErB;AACA,SAAS,aAAa,OAAO,OAAO;AAChC,MAAI,UAAU,MAAM;AAChB,WAAO;AAAA,MACH,MAAM;AAAA,IAAA;AAAA,EAEd;AACA;AACI,QAAI,0BAA0B,KAAK,GAAG;AAClC,aAAO,8BAA8B,OAAO,KAAK;AAAA,IACrD;AACA,QAAI,4BAA4B,KAAK,GAAG;AACpC,aAAO,gCAAgC,OAAO,KAAK;AAAA,IACvD;AACA,QAAI,aAAa,KAAK,GAAG;AACrB,aAAO,iBAAiB,OAAO,KAAK;AAAA,IACxC;AACA,QAAI,sBAAsB,KAAK,GAAG;AAC9B,aAAO,0BAA0B,OAAO,KAAK;AAAA,IACjD;AAAA,EACJ;AACA,MAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,WAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,MACT,OAAO;AAAA,QACH,YAAY,MAAM,IAAI,CAAC,GAAG,MAAM,mBAAmB,GAAG,GAAG,QAAQ,CAAC,CAAC;AAAA,MAAA;AAAA,IACvE;AAAA,EAER;AACA,MAAI,iBAAiB,KAAK;AACtB,WAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,MACT,WAAW;AAAA,MACX,aAAa,OAAO,MAAM,IAAI;AAAA,MAC9B,OAAO;AAAA,QACH,SAAS,MAAM,KAAK,KAAK,EAAE,IAAI,CAAC,MAAM,eAAe,GAAG,QAAQ,CAAC,CAAC;AAAA,MAAA;AAAA,IACtE;AAAA,EAER;AACA,MAAI,iBAAiB,KAAK;AACtB,WAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,MACT,WAAW;AAAA,MACX,aAAa,OAAO,MAAM,IAAI;AAAA,MAC9B,OAAO;AAAA,QACH,SAAS,MAAM,KAAK,MAAM,SAAS,EAAE,IAAI,CAAC,MAAM,eAAe,GAAG,QAAQ,CAAC,CAAC;AAAA,MAAA;AAAA,IAChF;AAAA,EAER;AACA,MAAI,iBAAiB,SAAS;AAC1B,WAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,MACT,OAAO;AAAA,QACH,YAAY,CAAA;AAAA,MAAC;AAAA,IACjB;AAAA,EAER;AACA,MAAI,iBAAiB,QAAQ;AACzB,WAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,MACT,OAAO,OAAO,KAAK;AAAA,MACnB,WAAW;AAAA,IAAA;AAAA,EAEnB;AACA,MAAI,iBAAiB,MAAM;AACvB,WAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,MACT,OAAO,OAAO,KAAK;AAAA,MACnB,WAAW;AAAA,IAAA;AAAA,EAEnB;AACA,MAAI,iBAAiB,OAAO;AACxB,WAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS;AAAA,MACT,OAAO,MAAM,WAAW,OAAO,KAAK;AAAA,MACpC,WAAW,MAAM,QAAQ;AAAA,IAAA;AAAA,EAEjC;AACA,MAAI,YAAY;AAChB;AACI,UAAM,cAAc,MAAM;AAC1B,QAAI,aAAa;AAEb,UAAI,YAAY,kBAAkB;AAE9B,oBAAY,YAAY,mBAAmB;AAAA,MAC/C;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA,OAAO;AAAA,MACH,YAAY,OAAO,QAAQ,KAAK,EAAE,IAAI,CAAC,UAAU,qBAAqB,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC;AAAA,IAAA;AAAA,EACxG;AAER;AACA,SAAS,0BAA0B,OAAO;AACtC,SAAO,MAAM,KAAK,4BAA4B,MAAM,CAAC;AACzD;AACA,SAAS,4BAA4B,OAAO;AACxC,SAAO,MAAM,QAAQ,MAAM,OAAO,QAAQ,MAAM;AACpD;AACA,SAAS,8BAA8B,OAAO,OAAO;AACjD,SAAO;AAAA,IACH,MAAM;AAAA,IACN,WAAW;AAAA,IACX,OAAO;AAAA,MACH,YAAY,OAAO,QAAQ,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,MAAMC,MAAK,MAAM,qBAAqB,MAAMA,QAAO,QAAQ,CAAC,CAAC;AAAA,IAAA;AAAA,EAChH;AAER;AACA,SAAS,gCAAgC,OAAO,OAAO;AACnD,SAAO;AAAA,IACH,MAAM;AAAA,IACN,WAAW;AAAA,IACX,OAAO;AAAA,MACH,YAAY,OAAO,QAAQ,MAAM,IAAI,EAAE,IAAI,CAAC,CAAC,MAAMA,MAAK,MAAM,qBAAqB,MAAMA,QAAO,QAAQ,CAAC,CAAC;AAAA,IAAA;AAAA,EAC9G;AAER;AACA,SAAS,aAAa,OAAO;AACzB,SAAO,MAAM,SAAS,MAAM,WAAW,QAAQ,MAAM,YAAY;AACrE;AACA,SAAS,iBAAiB,OAAO,OAAO;AACpC,SAAO;AAAA,IACH,MAAM;AAAA;AAAA;AAAA,IAGN,OAAO;AAAA,MACH,YAAY,OAAO,QAAQ,KAAK,EAC3B,OAAO,CAAC,CAAC,IAAI,MAAM;AAAA,QACpB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MAAA,EACF,SAAS,IAAI,CAAC,EACX,IAAI,CAAC,CAAC,MAAMA,MAAK,MAAM,qBAAqB,MAAMA,QAAO,QAAQ,CAAC,CAAC;AAAA,IAAA;AAAA,EAC5E;AAER;AACA,SAAS,sBAAsB,OAAO;AAClC,SAAQ,OAAO,MAAM,qBAAqB,cACtC,OAAO,MAAM,gBAAgB,cAC7B,MAAM;AACd;AACA,SAAS,0BAA0B,OAAO,OAAO;AAC7C,SAAO;AAAA,IACH,MAAM;AAAA,IACN,OAAO;AAAA,MACH,YAAY,OAAO,QAAQ,MAAM,OAAO,EAAE,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,qBAAqB,MAAM,OAAO,QAAQ,CAAC,CAAC;AAAA,IAAA;AAAA,EACjH;AAER;AACA,SAAS,qBAAqB,MAAM,OAAO,OAAO;AAC9C,QAAM,SAAS,UAAU,OAAO,KAAK;AACrC,SAAO,OAAO;AACd,SAAO;AACX;AACA,SAAS,mBAAmB,OAAO,OAAO,OAAO;AAC7C,QAAM,SAAS,UAAU,OAAO,KAAK;AACrC,SAAO,OAAO,GAAG,KAAK;AACtB,SAAO;AACX;AACA,SAAS,eAAe,OAAO,OAAO;AAClC,SAAO;AAAA,IACH,OAAO,UAAU,OAAO,KAAK;AAAA,EAAA;AAErC;AACA,SAAS,eAAe,OAAO,OAAO;AAClC,SAAO;AAAA,IACH,KAAK,UAAU,MAAM,CAAC,GAAG,KAAK;AAAA,IAC9B,OAAO,UAAU,MAAM,CAAC,GAAG,KAAK;AAAA,EAAA;AAExC;AAEA,MAAM,gBAAgB,CAAC,OAAO,QAAQ,SAAS,QAAQ,OAAO;AAC9D,IAAI,cAAc;AAClB,MAAM,eAAe,CAAA;AACrB,MAAM,eAAe,CAAA;AACrB,SAAS,oBAAoB,UAAU;AACnC,MAAI,eAAe,MAAM;AACrB,iBAAa,KAAK,GAAG,QAAQ;AAC7B;AAAA,EACJ;AACA,cAAY,KAAK,UAAU,OAAO,OAAO;AAAA,IACrC,MAAM;AAAA,IACN,MAAM;AAAA,EAAA,GACP,YAAY,CAAC,CAAC;AACrB;AACA,SAAS,eAAe,OAAO,QAAQ,IAAI;AACvC,gBAAc;AACd,SAAO,OAAO,cAAc,KAAK;AACjC,MAAI,SAAS,QAAQ,aAAa,SAAS,GAAG;AAC1C,UAAM,WAAW,aAAa,MAAA;AAC9B,iBAAa,SAAS;AACtB,wBAAoB,QAAQ;AAAA,EAChC;AACJ;AACA,MAAM,kBAAgC,8BAAc,OAAO,CAAC,SAAS,SAAS;AAC1E,UAAQ,IAAI,IAAI,QAAQ,IAAI,EAAE,KAAK,OAAO;AAC1C,SAAO;AACX,GAAG,EAAE;AACL,MAAM,cAAc;AACpB,SAAS,iBAAiB;AACtB,WAAS,YAAY,MAAM;AACvB,WAAO,YAAa,MAAM;AACtB,YAAM,eAAe,CAAC,GAAG,IAAI;AAC7B,UAAI,aAAa,QAAQ;AACrB,cAAM,cAAc,aAAa,aAAa,SAAS,CAAC;AAExD,YAAI,OAAO,gBAAgB,YAAY,YAAY,KAAK,WAAW,GAAG;AAClE,uBAAa,IAAA;AAAA,QACjB;AAAA,MACJ;AAC2C;AACvC,wBAAgB,IAAI,EAAE,GAAG,YAAY;AAAA,MACzC;AACA,0BAAoB,CAAC,cAAc,MAAM,IAAI,CAAC,CAAC;AAAA,IACnD;AAAA,EACJ;AAEA,MAAI,qBAAqB;AACrB,kBAAc,QAAQ,CAAC,SAAS;AAC5B,cAAQ,IAAI,IAAI,YAAY,IAAI;AAAA,IACpC,CAAC;AACD,WAAO,SAAS,iBAAiB;AAC7B,oBAAc,QAAQ,CAAC,SAAS;AAC5B,gBAAQ,IAAI,IAAI,gBAAgB,IAAI;AAAA,MACxC,CAAC;AAAA,IACL;AAAA,EACJ,OACK;AACD;AACI,UAAI,OAAOD,oDAAAA,UAAQ,eAAeA,oDAAAA,MAAI,OAAO;AACzC,cAAM,SAASA,oDAAAA,MAAI;AACnB,YAAI,QAAQ;AAERA,oEAAI,QAAQ,YAAa,MAAM;AAC3B,kBAAM,CAAC,MAAM,UAAU,GAAG,IAAI,IAAI;AAElC,mBAAO,MAAM,IAAI,GAAG,IAAI;AACxB,gCAAoB,CAAC,cAAc,MAAM,CAAC,GAAG,MAAM,QAAQ,CAAC,CAAC,CAAC;AAAA,UAClE;AACA,iBAAO,SAAS,iBAAiB;AAC7BA,gEAAAA,MAAI,QAAQ;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,SAAS,iBAAiB;AAAA,EAAE;AACvC;AACA,SAAS,oBAAoB;AACzB,QAAM,QAAQ,QAAQ;AACtB,QAAM,MAAM,OAAA;AACZ,MAAI;AAEA,YAAQ,MAAM;AAAA,EAClB,SACO,IAAI;AACP,WAAO;AAAA,EACX;AAEA,QAAM,aAAa,QAAQ,QAAQ;AACnC,UAAQ,MAAM;AACd,SAAO;AACX;AAEA,IAAI,YAAY;AAKhB,MAAM,iCAAiB,IAAA;AACvB,MAAM,aAAa,CAAA;AACnB,SAAS,kBAAkB,QAAQ;AAC/B,MAAI,aAAa,MAAM;AACnB,WAAO,QAAQ,CAAC,UAAU;AACtB,iBAAW,IAAI,KAAK;AAAA,IACxB,CAAC;AACD;AAAA,EACJ;AACA,QAAM,OAAO,OACR,IAAI,CAAC,QAAQ;AACd,UAAM,qBAAqB,OAAO,aAAa,OAAO,YAAY;AAClE,UAAM,SAAS,qBAAqB,gCAAgC;AACpE,QAAI,oBAAoB;AACpB,YAAM,IAAI;AAAA,IACd;AACA,QAAI,eAAe,SAAS,IAAI,OAAO;AACnC,UAAI,IAAI,WAAW,CAAC,IAAI,MAAM,SAAS,IAAI,OAAO,GAAG;AACjD,eAAO,GAAG,MAAM,GAAG,IAAI,OAAO;AAAA,EAC5C,IAAI,KAAK;AAAA,MACC;AACA,aAAO,GAAG,MAAM,GAAG,IAAI,KAAK;AAAA,IAChC;AACA,QAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;AACzC,UAAI;AACA,eAAO,SAAS,KAAK,UAAU,GAAG;AAAA,MACtC,SACOE,MAAK;AACR,eAAO,SAAS,OAAOA,IAAG;AAAA,MAC9B;AAAA,IACJ;AACA,WAAO,SAAS,OAAO,GAAG;AAAA,EAC9B,CAAC,EACI,OAAO,OAAO;AACnB,MAAI,KAAK,SAAS,GAAG;AACjB,cAAU,KAAK,UAAU,OAAO,OAAO;AAAA,MACnC,MAAM;AAAA,MACN;AAAA,IAAA,GACD,UAAU,CAAC,CAAC;AAAA,EACnB;AACJ;AACA,SAAS,aAAa,OAAO,QAAQ,IAAI;AACrC,cAAY;AACZ,SAAO,OAAO,YAAY,KAAK;AAC/B,MAAI,SAAS,QAAQ,WAAW,OAAO,GAAG;AACtC,UAAM,SAAS,MAAM,KAAK,UAAU;AACpC,eAAW,MAAA;AACX,sBAAkB,MAAM;AAAA,EAC5B;AACJ;AACA,SAAS,cAAc;AACnB,WAAS,QAAQ,OAAO;AACpB,QAAI;AAIA,UAAI,OAAO,0CAA0B,eACjC,iBAAiB,aAAA,uBAAA,KACjB,MAAM,kBAAkB,SACxB,MAAM,OAAO,WACb,MAAM,OAAO,QAAQ,SAAS,mDAAmD,GAAG;AACpF;AAAA,MACJ;AACA,UAAI,MAAuC;AACvC,wBAAgB,MAAM,KAAK;AAAA,MAC/B;AACA,wBAAkB,CAAC,KAAK,CAAC;AAAA,IAC7B,SACO,KAAK;AACR,sBAAgB,MAAM,GAAG;AAAA,IAC7B;AAAA,EACJ;AACA,MAAI,OAAOF,oDAAAA,MAAI,YAAY,YAAY;AACnCA,wDAAAA,MAAI,QAAQ,OAAO;AAAA,EACvB;AACA,MAAI,OAAOA,oDAAAA,MAAI,yBAAyB,YAAY;AAChDA,wDAAAA,MAAI,qBAAqB,OAAO;AAAA,EACpC;AACA,SAAO,SAAS,WAAW;AACvB,QAAI,OAAOA,oDAAAA,MAAI,aAAa,YAAY;AACpCA,0DAAAA,MAAI,SAAS,OAAO;AAAA,IACxB;AACA,QAAI,OAAOA,oDAAAA,MAAI,0BAA0B,YAAY;AACjDA,0DAAAA,MAAI,sBAAsB,OAAO;AAAA,IACrC;AAAA,EACJ;AACJ;AAEA,SAAS,2BAA2B;AAChC,QAAM,QAAQ;AACd,QAAM,OAAO;AACb,QAAM,KAAK;AAIX,QAAM,OAAO,OAAO,SAAS;AAE7B,MAAI,eAAe,OAAO,MAAM;AAAA,EAAE,IAAI,YAAA;AACtC,MAAI,iBAAiB,OAAO,MAAM;AAAA,EAAE,IAAI,eAAA;AAExC,SAAO,QAAQ,UAAU,KAAK,MAAM;AAChC,QAAI,MAAM;AACN,qBAAe,YAAA;AACf,uBAAiB,eAAA;AAAA,IACrB;AACA,WAAO,kBAAkB,OAAO,MAAM,EAAE,EAAE,KAAK,CAAC,WAAW;AACvD,UAAI,CAAC,QAAQ;AACT,qBAAA;AACA,uBAAA;AACA,wBAAgB,MAAM,UAAU,0BAA0B,CAAC;AAC3D,wBAAgB,MAAM,UAAU,wBAAwB,CAAC;AACzD,wBAAgB,MAAM,UAAU,0BAA0B,CAAC;AAC3D,eAAO;AAAA,MACX;AACA,gCAAA;AACA,aAAO,QAAQ,MAAM;AAIjB,wBAAgB,MAAM,UAAU,2CAA2C,CAAC;AAC5E,qBAAA;AACA,uBAAA;AAAA,MACJ,CAAC;AACD,qBAAe,CAAC,SAAS;AAIrB,eAAO,KAAK;AAAA,UACR;AAAA,QAAA,CACH;AAAA,MACL,CAAC;AACD,mBAAa,CAAC,SAAS;AAInB,eAAO,KAAK;AAAA,UACR;AAAA,QAAA,CACH;AAAA,MACL,CAAC;AACD,aAAO;AAAA,IACX,CAAC;AAAA,EACL,CAAC;AACL;AACA,MAAM,aAAa;AACnB,SAAS,UAAU,OAAO;AACtB,SAAO,GAAG,UAAU,GAAG,KAAK,GAAG,UAAU;AAC7C;AACA,SAAS,4BAA4B;AACjC,MAAI,OAAOG,oDAAAA,SAAO,aAAa;AAE3BA,wDAAAA,KAAG,kBAAkB;AAAA,EAEzB,WACS,OAAO,OAAO,aAAa;AAEhC,OAAG,kBAAkB;AAAA,EACzB,WACS,OAAO,OAAO,aAAa;AAChC,OAAG,kBAAkB;AAAA,EACzB,WACS,OAAO,SAAS,aAAa;AAClC,SAAK,kBAAkB;AAAA,EAC3B,WACS,OAAO,OAAO,aAAa;AAChC,OAAG,kBAAkB;AAAA,EACzB,WACS,OAAO,OAAO,aAAa;AAChC,OAAG,kBAAkB;AAAA,EACzB,WACS,OAAO,OAAO,aAAa;AAChC,OAAG,kBAAkB;AAAA,EACzB,WACS,OAAO,QAAQ,aAAa;AACjC,QAAI,kBAAkB;AAAA,EAC1B,WACS,OAAO,QAAQ,aAAa;AACjC,QAAI,kBAAkB;AAAA,EAC1B,WACS,OAAO,OAAO,aAAa;AAChC,OAAG,kBAAkB;AAAA,EACzB;AACJ;AACA,yBAAA;", "x_google_ignoreList": [0]}