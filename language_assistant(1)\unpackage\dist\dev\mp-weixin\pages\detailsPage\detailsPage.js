"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const stores_counter = require("../../stores/counter.js");
const request_api = require("../../request/api.js");
const stores_app = require("../../stores/app.js");
if (!Array) {
  const _component_CustomNavbar = common_vendor.resolveComponent("CustomNavbar");
  const _easycom_uni_data_select2 = common_vendor.resolveComponent("uni-data-select");
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  (_component_CustomNavbar + _easycom_uni_data_select2 + _easycom_uni_icons2)();
}
const _easycom_uni_data_select = () => "../../uni_modules/uni-data-select/components/uni-data-select/uni-data-select.js";
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
if (!Math) {
  (PlatformCanvas + _easycom_uni_data_select + _easycom_uni_icons)();
}
const PlatformCanvas = () => "../../components/PlatformCanvas.js";
const _sfc_main = {
  __name: "detailsPage",
  setup(__props) {
    const {
      t
    } = common_vendor.useI18n();
    stores_counter.useUserStore();
    const appStores = stores_app.appStore();
    const isModelLoading = common_vendor.ref(true);
    const showFallback = common_vendor.ref(false);
    const threeData = common_vendor.reactive({
      renderer: null,
      scene: null,
      camera: null,
      model: null,
      mixer: null,
      clock: null,
      animationId: null,
      controls: null
    });
    const touchData = common_vendor.reactive({
      lastX: 0,
      lastY: 0,
      isRotating: false
    });
    const locationRange = common_vendor.ref([]);
    const sceneRange = common_vendor.ref([]);
    common_vendor.ref({});
    const locationValue = common_vendor.ref(1);
    const sceneValue = common_vendor.ref(1);
    const isRecording = common_vendor.ref(false);
    const recorderManager = common_vendor.index.getRecorderManager();
    const innerAudioContext = common_vendor.index.createInnerAudioContext();
    const recordStartTime = common_vendor.ref(0);
    const willCancel = common_vendor.ref(false);
    const touchStartY = common_vendor.ref(0);
    const isTextMode = common_vendor.ref(true);
    const scrollToMessage = common_vendor.ref("");
    const languagename = common_vendor.ref("普通话");
    const languagename_uy = common_vendor.ref("");
    const title = common_vendor.ref("语言助手");
    const inputMessage = common_vendor.ref("");
    const messages = common_vendor.ref([{
      sender: "system",
      avatar: "/static/mine/avatar.jpg",
      content: "你好！咱们开始普通话训练吧！"
    }]);
    const categories = [
      {
        key: "dialog",
        label: t("detailsPage.switchDialog")
      },
      {
        key: "level",
        label: t("detailsPage.adjustDifficulty")
      },
      {
        key: "vocab",
        label: t("detailsPage.vocabularyExplanation")
      },
      {
        key: "grammar",
        label: t("detailsPage.grammarExplanation")
      },
      {
        key: "grammar",
        label: t("detailsPage.hello")
      },
      {
        key: "grammar",
        label: t("detailsPage.iAmGood")
      },
      {
        key: "grammar",
        label: t("detailsPage.goodMorning")
      }
    ];
    const activeCategory = common_vendor.ref("dialog");
    const pageAgentId = common_vendor.ref(null);
    common_vendor.watch(messages, async () => {
      await common_vendor.nextTick$1();
      scrollToBottom();
    }, {
      deep: true
    });
    const scrollToBottom = () => {
      common_vendor.nextTick$1(() => {
        if (messages.value.length > 0) {
          scrollToMessage.value = "msg-" + (messages.value.length - 1);
        }
      });
    };
    const onScrollToUpper = () => {
      console.log("滚动到顶部");
    };
    const sendMessage = () => {
      if (!inputMessage.value.trim()) {
        return;
      }
      messages.value.push({
        sender: "user",
        avatar: "/static/mine/avatar.jpg",
        content: inputMessage.value
      });
      const userMsg = inputMessage.value;
      inputMessage.value = "";
      setTimeout(() => {
        messages.value.push({
          sender: "system",
          avatar: "/static/mine/avatar.jpg",
          content: "收到你的信息：" + userMsg
        });
      }, 800);
    };
    const playVoice = (msg) => {
      innerAudioContext.src = msg.voiceUrl;
      innerAudioContext.play();
      if (msg.unread) {
        msg.unread = false;
      }
      innerAudioContext.onEnded(() => {
        console.log("语音播放结束");
      });
    };
    const startRecord = (e) => {
      touchStartY.value = e.changedTouches[0].clientY;
      willCancel.value = false;
      recordStartTime.value = Date.now();
      recorderManager.start({
        format: "mp3",
        duration: 6e4,
        // 最长录音时间，单位ms
        sampleRate: 16e3,
        // 采样率
        numberOfChannels: 1,
        // 录音通道数
        encodeBitRate: 48e3
        // 编码码率
      });
      isRecording.value = true;
    };
    const onTouchMove = (e) => {
      if (isRecording.value) {
        const currentY = e.changedTouches[0].clientY;
        const moveDistance = touchStartY.value - currentY;
        willCancel.value = moveDistance > 50;
      }
    };
    const stopRecord = () => {
      if (isRecording.value) {
        if (willCancel.value) {
          cancelRecord();
        } else {
          recorderManager.stop();
          isRecording.value = false;
        }
      }
    };
    const cancelRecord = () => {
      if (isRecording.value) {
        recorderManager.stop();
        isRecording.value = false;
        willCancel.value = false;
        common_vendor.index.showToast({
          title: $t("detailsPage.recordCanceled"),
          icon: "none"
        });
      }
    };
    recorderManager.onStop((res) => {
      console.log("录音结束", res);
      isRecording.value = false;
      willCancel.value = false;
      if (!willCancel.value) {
        const duration = Math.round((Date.now() - recordStartTime.value) / 1e3);
        messages.value.push({
          sender: "user",
          avatar: "/static/mine/avatar.jpg",
          content: "[语音消息]",
          type: "voice",
          voiceUrl: res.tempFilePath,
          duration,
          unread: true
        });
        setTimeout(() => {
          messages.value.push({
            sender: "system",
            avatar: "/static/mine/avatar.jpg",
            content: "收到你的语音消息",
            type: "voice",
            voiceUrl: res.tempFilePath,
            duration,
            unread: true
          });
        }, 800);
      }
    });
    recorderManager.onError((res) => {
      console.error("录音错误:", res);
      isRecording.value = false;
      willCancel.value = false;
      common_vendor.index.showToast({
        title: "录音失败",
        icon: "none"
      });
    });
    const switchInputMode = () => {
      isTextMode.value = !isTextMode.value;
    };
    const switchCategory = (key) => {
      activeCategory.value = key;
      console.log("切换到类别:", key);
    };
    const getScholarsAndScenes = async (agent_id) => {
      try {
        const res = await request_api.scholars_scenes(agent_id);
        locationRange.value = (res.data.scholars || []).map((item) => ({
          value: item.id,
          text: appStores.lang === "zh-Ug" ? item.name_uy : item.name
        }));
        sceneRange.value = (res.data.scenes || []).map((item) => ({
          value: item.id,
          text: appStores.lang === "zh-Ug" ? item.name_uy : item.name
        }));
      } catch (e) {
        common_vendor.index.showToast({
          title: "获取下拉数据失败",
          icon: "none"
        });
      }
    };
    common_vendor.onShow(() => {
      getScholarsAndScenes(pageAgentId.value);
    });
    common_vendor.onLoad((options) => {
      const id = options.id;
      pageAgentId.value = id;
      const name = options.name ? decodeURIComponent(options.name) : "";
      if (name) {
        languagename.value = name;
      }
      const name_uy = options.name_uy ? decodeURIComponent(options.name_uy) : "";
      if (name_uy) {
        languagename_uy.value = name_uy;
      }
    });
    const useCanvas = ({ canvas, useFrame, recomputeSize }) => {
      console.log("Canvas初始化成功:", canvas);
      initThreeScene(canvas);
    };
    const initThreeScene = (canvas, useFrame, recomputeSize) => {
      try {
        console.log("开始初始化Three.js场景...");
        const CANVAS_WIDTH = canvas.width;
        const CANVAS_HEIGHT = canvas.height;
        threeData.renderer = new common_vendor.WebGLRenderer({
          canvas,
          antialias: true,
          alpha: true
        });
        threeData.renderer.setPixelRatio(common_vendor.index.getSystemInfoSync().pixelRatio || 1);
        threeData.renderer.setSize(CANVAS_WIDTH, CANVAS_HEIGHT);
        threeData.renderer.toneMapping = common_vendor.ACESFilmicToneMapping;
        threeData.renderer.toneMappingExposure = 1;
        threeData.camera = new common_vendor.PerspectiveCamera(45, CANVAS_WIDTH / CANVAS_HEIGHT, 1, 2e3);
        threeData.camera.position.set(0, 100, 200);
        threeData.scene = new common_vendor.Scene();
        threeData.scene.background = new common_vendor.Color(16119802);
        const ambientLight = new common_vendor.AmbientLight(16777215, 0.8);
        threeData.scene.add(ambientLight);
        const directionalLight = new common_vendor.DirectionalLight(16777215, 0.6);
        directionalLight.position.set(5, 10, 5);
        threeData.scene.add(directionalLight);
        threeData.clock = new common_vendor.Clock();
        console.log("Three.js场景初始化成功");
        startRenderLoop();
        loadGLBModel();
        isModelLoading.value = false;
      } catch (error) {
        console.error("Three.js场景初始化失败:", error);
        showFallback.value = true;
        isModelLoading.value = false;
      }
    };
    const loadGLBModel = () => {
      try {
        "../../common/vendor.js".then((n) => n.GLTFLoader).then(({ GLTFLoader }) => {
          const loader = new GLTFLoader();
          const modelPaths = [
            "http://minhandash.edu.izdax.cn/Teacher_001.glb",
            // 网络URL（参考项目使用的）
            "/static/Teacher.glb"
            // 本地路径
          ];
          const tryLoadModel = (pathIndex = 0) => {
            if (pathIndex >= modelPaths.length) {
              console.error("所有模型路径都加载失败");
              showFallback.value = true;
              isModelLoading.value = false;
              return;
            }
            const modelPath = modelPaths[pathIndex];
            console.log(`尝试加载GLB模型 (${pathIndex + 1}/${modelPaths.length}):`, modelPath);
            loader.load(
              modelPath,
              (gltf) => {
                console.log("GLB模型加载成功!", gltf);
                threeData.model = gltf.scene;
                threeData.model.scale.set(220, 220, 220);
                threeData.model.position.set(0, -50, 0);
                threeData.scene.add(threeData.model);
                if (gltf.animations && gltf.animations.length > 0) {
                  threeData.mixer = new common_vendor.AnimationMixer(threeData.model);
                  console.log(`找到 ${gltf.animations.length} 个动画`);
                  const action = threeData.mixer.clipAction(gltf.animations[0]);
                  action.play();
                }
                console.log("Teacher 3D模型加载并显示成功!");
                showFallback.value = false;
                isModelLoading.value = false;
              },
              (progress) => {
                if (progress.total > 0) {
                  const percent = (progress.loaded / progress.total * 100).toFixed(1);
                  console.log(`模型加载进度: ${percent}%`);
                }
              },
              (error) => {
                console.error(`GLB模型加载失败 (路径 ${pathIndex + 1}):`, error);
                tryLoadModel(pathIndex + 1);
              }
            );
          };
          tryLoadModel();
        }).catch((error) => {
          console.error("GLTFLoader导入失败:", error);
          showFallback.value = true;
          isModelLoading.value = false;
        });
      } catch (error) {
        console.error("GLB模型加载初始化失败:", error);
        showFallback.value = true;
        isModelLoading.value = false;
      }
    };
    const startRenderLoop = () => {
      const render = () => {
        if (!threeData.renderer || !threeData.scene || !threeData.camera)
          return;
        if (threeData.mixer && threeData.clock) {
          const delta = threeData.clock.getDelta();
          threeData.mixer.update(delta);
        }
        threeData.renderer.render(threeData.scene, threeData.camera);
        threeData.animationId = requestAnimationFrame(render);
      };
      render();
    };
    const onTouchStart = (e) => {
      console.log("3D模型触摸开始", e);
      if (e.touches && e.touches.length > 0) {
        touchData.lastX = e.touches[0].clientX;
        touchData.lastY = e.touches[0].clientY;
        touchData.isRotating = true;
      }
    };
    const onTouch3DMove = (e) => {
      if (!touchData.isRotating || !e.touches || e.touches.length === 0)
        return;
      const touch = e.touches[0];
      const moveX = touch.clientX - touchData.lastX;
      const moveY = touch.clientY - touchData.lastY;
      if (threeData.model) {
        threeData.model.rotation.y += moveX * 0.01;
        threeData.model.rotation.x += moveY * 0.01;
      }
      touchData.lastX = touch.clientX;
      touchData.lastY = touch.clientY;
      console.log("3D模型旋转中...", { moveX, moveY });
    };
    const onTouchEnd = (e) => {
      console.log("3D模型触摸结束", e);
      touchData.isRotating = false;
    };
    const cleanup = () => {
      if (threeData.animationId) {
        cancelAnimationFrame(threeData.animationId);
        threeData.animationId = null;
      }
      if (threeData.renderer) {
        threeData.renderer.dispose();
        threeData.renderer = null;
      }
      if (threeData.scene) {
        threeData.scene.clear();
        threeData.scene = null;
      }
      if (threeData.mixer) {
        threeData.mixer = null;
      }
      console.log("Three.js资源已清理");
    };
    common_vendor.onMounted(async () => {
      console.log("UniApp页面挂载完成");
      common_vendor.index.$on("language-changed", () => {
        locationRange.value = [
          {
            value: 1,
            text: t("detailsPage.studentParent")
          },
          {
            value: 2,
            text: t("detailsPage.teacher")
          },
          {
            value: 3,
            text: t("detailsPage.student")
          }
        ];
        sceneRange.value = [
          {
            value: 1,
            text: t("detailsPage.parentMeeting")
          },
          {
            value: 2,
            text: t("detailsPage.classroom")
          },
          {
            value: 3,
            text: t("detailsPage.teachingResearch")
          }
        ];
        title.value = t("detailsPage.title");
      });
    });
    common_vendor.onUnmounted(() => {
      console.log("页面卸载，清理Three.js资源");
      cleanup();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          title: common_vendor.unref(t)("home.aiLanguageAssistant"),
          back: true
        }),
        b: !showFallback.value
      }, !showFallback.value ? {
        c: common_vendor.o(useCanvas),
        d: common_vendor.o(onTouchStart),
        e: common_vendor.o(onTouch3DMove),
        f: common_vendor.o(onTouchEnd),
        g: common_vendor.p({
          type: "webgl",
          ["canvas-id"]: "webgl-3d-model"
        })
      } : {}, {
        h: isModelLoading.value
      }, isModelLoading.value ? {} : {}, {
        i: showFallback.value
      }, showFallback.value ? {} : {}, {
        j: common_assets._imports_0$2,
        k: common_vendor.t(common_vendor.unref(appStores).lang === "zh-Ug" ? languagename_uy.value : languagename.value),
        l: common_vendor.t(_ctx.$t("detailsPage.location")),
        m: common_vendor.o(($event) => locationValue.value = $event),
        n: common_vendor.p({
          localdata: locationRange.value,
          clear: false,
          modelValue: locationValue.value
        }),
        o: common_vendor.p({
          type: "right",
          size: "20",
          color: "#006397"
        }),
        p: common_vendor.t(_ctx.$t("detailsPage.scene")),
        q: common_vendor.o(($event) => sceneValue.value = $event),
        r: common_vendor.p({
          localdata: sceneRange.value,
          clear: false,
          modelValue: sceneValue.value
        }),
        s: common_vendor.t(_ctx.$t("detailsPage.startTraining")),
        t: common_assets._imports_1$2,
        v: common_assets._imports_2,
        w: common_vendor.f(messages.value, (msg, idx, i0) => {
          return common_vendor.e({
            a: msg.sender === "user"
          }, msg.sender === "user" ? common_vendor.e({
            b: msg.avatar
          }, msg.avatar ? {
            c: msg.avatar
          } : {}, {
            d: msg.type !== "voice"
          }, msg.type !== "voice" ? {
            e: common_vendor.t(msg.content)
          } : common_vendor.e({
            f: "d78077b6-5-" + i0,
            g: common_vendor.p({
              type: "sound",
              size: "24",
              color: "#222"
            }),
            h: common_vendor.t(msg.duration),
            i: msg.unread
          }, msg.unread ? {} : {}, {
            j: common_vendor.o(($event) => playVoice(msg), idx)
          }), {
            k: "msg-" + idx
          }) : msg.sender === "system" ? common_vendor.e({
            m: common_assets._imports_3,
            n: msg.type !== "voice"
          }, msg.type !== "voice" ? {
            o: common_vendor.t(msg.content)
          } : common_vendor.e({
            p: "d78077b6-6-" + i0,
            q: common_vendor.p({
              type: "sound",
              size: "24",
              color: "#1677c7"
            }),
            r: common_vendor.t(msg.duration),
            s: msg.unread
          }, msg.unread ? {} : {}, {
            t: common_vendor.o(($event) => playVoice(msg), idx)
          }), {
            v: "msg-" + idx
          }) : {}, {
            l: msg.sender === "system",
            w: idx
          });
        }),
        x: scrollToMessage.value,
        y: common_vendor.o(onScrollToUpper),
        z: common_vendor.f(categories, (item, index, i0) => {
          return {
            a: common_vendor.t(item.label),
            b: item.key,
            c: activeCategory.value === item.key ? 1 : "",
            d: common_vendor.o(($event) => switchCategory(item.key), item.key)
          };
        }),
        A: isTextMode.value ? "/static/icons/jianpan.svg" : "/static/icons/maikefeng.svg",
        B: common_vendor.o(switchInputMode),
        C: isTextMode.value
      }, isTextMode.value ? {
        D: _ctx.$t("detailsPage.inputMessage"),
        E: inputMessage.value,
        F: common_vendor.o(($event) => inputMessage.value = $event.detail.value)
      } : {
        G: common_vendor.t(_ctx.$t("detailsPage.holdToSpeak")),
        H: common_vendor.o(startRecord),
        I: common_vendor.o(stopRecord),
        J: common_vendor.o(cancelRecord),
        K: common_vendor.o(onTouchMove)
      }, {
        L: common_vendor.t(_ctx.$t("detailsPage.send")),
        M: common_vendor.o(sendMessage),
        N: isRecording.value
      }, isRecording.value ? common_vendor.e({
        O: willCancel.value
      }, willCancel.value ? {
        P: common_assets._imports_4
      } : {
        Q: common_vendor.f(3, (i, k0, i0) => {
          return {
            a: i,
            b: (i - 1) * 0.2 + "s"
          };
        })
      }, {
        R: common_vendor.t(willCancel.value ? _ctx.$t("detailsPage.releaseToCancel") : _ctx.$t("detailsPage.releaseToSend")),
        S: common_vendor.t(_ctx.$t("detailsPage.slideUpToCancel")),
        T: willCancel.value ? 1 : ""
      }) : {}, {
        U: common_vendor.n(common_vendor.unref(appStores).lang == "zh-Ug" ? "ug" : "")
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d78077b6"]]);
wx.createPage(MiniProgramPage);
