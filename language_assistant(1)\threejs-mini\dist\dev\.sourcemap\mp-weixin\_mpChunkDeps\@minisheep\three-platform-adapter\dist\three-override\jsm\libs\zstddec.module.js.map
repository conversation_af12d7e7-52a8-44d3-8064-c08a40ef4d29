{"version": 3, "file": "zstddec.module.js", "sources": ["../node_modules/@minisheep/three-platform-adapter/dist/three-override/jsm/libs/zstddec.module.js"], "sourcesContent": ["import{LocalAsset as e}from\"../utils/LocalAsset.js\";let t,r,o;const s={env:{emscripten_notify_memory_growth:function(e){o=new Uint8Array(r.exports.memory.buffer)}}};class Q{init(){return t||(t=WebAssembly.instantiate(e.resolve(\"wasm\",\"zstd_decoder.wasm\"),s).then(this._init),t)}_init(e){r=e.instance,s.env.emscripten_notify_memory_growth(0)}decode(e,t=0){if(!r)throw new Error(\"ZSTDDecoder: Await .init() before decoding.\");const s=e.byteLength,n=r.exports.malloc(s);o.set(e,n),t=t||Number(r.exports.ZSTD_findDecompressedSize(n,s));const i=r.exports.malloc(t),c=r.exports.ZSTD_decompress(i,t,n,s),m=o.slice(i,i+c);return r.exports.free(n),r.exports.free(i),m}}export{Q as ZSTDDecoder};\n"], "names": ["e", "t", "s"], "mappings": ";;AAAoD,IAAI,GAAE,GAAE;AAAE,MAAM,IAAE,EAAC,KAAI,EAAC,iCAAgC,SAAS,GAAE;AAAC,MAAE,IAAI,WAAW,EAAE,QAAQ,OAAO,MAAM;AAAC,EAAC,EAAC;AAAE,MAAM,EAAC;AAAA,EAAC,OAAM;AAAC,WAAO,MAAI,IAAE,aAAA,aAAA,EAAY,YAAYA,gGAAE,QAAQ,QAAO,mBAAmB,GAAE,CAAC,EAAE,KAAK,KAAK,KAAK,GAAE;AAAA,EAAE;AAAA,EAAC,MAAM,GAAE;AAAC,QAAE,EAAE,UAAS,EAAE,IAAI,gCAAgC,CAAC;AAAA,EAAC;AAAA,EAAC,OAAO,GAAEC,KAAE,GAAE;AAAC,QAAG,CAAC,EAAE,OAAM,IAAI,MAAM,6CAA6C;AAAE,UAAMC,KAAE,EAAE,YAAW,IAAE,EAAE,QAAQ,OAAOA,EAAC;AAAE,MAAE,IAAI,GAAE,CAAC,GAAED,KAAEA,MAAG,OAAO,EAAE,QAAQ,0BAA0B,GAAEC,EAAC,CAAC;AAAE,UAAM,IAAE,EAAE,QAAQ,OAAOD,EAAC,GAAE,IAAE,EAAE,QAAQ,gBAAgB,GAAEA,IAAE,GAAEC,EAAC,GAAE,IAAE,EAAE,MAAM,GAAE,IAAE,CAAC;AAAE,WAAO,EAAE,QAAQ,KAAK,CAAC,GAAE,EAAE,QAAQ,KAAK,CAAC,GAAE;AAAA,EAAC;AAAC;;", "x_google_ignoreList": [0]}