"use strict";
const _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm = require("../../../@dcloudio/uni-mp-weixin/dist/uni.api.esm.js");
const _mpChunkDeps__minisheep_threePlatformAdapter_dist_eventManager = require("./eventManager.js");
const _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter = require("./Adapter.js");
function x(e, t = _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm.wx$1) {
  return new Promise((n) => {
    setTimeout(() => {
      t.createSelectorQuery().select(e).fields({ node: true, id: true, rect: true, size: true }).exec((e2) => {
        if (!e2 || !e2[0] || e2.length > 1) throw new Error("invalid selector");
        n(e2[0]);
      });
    }, 0);
  });
}
const y = Date.now(), A = _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.y(_mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm.wx$1.getWindowInfo, _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm.wx$1.onWindowResize, _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm.wx$1.offWindowResize);
const E = globalThis;
"function" != typeof E.Worker && (E.Worker = _mpChunkDeps__minisheep_threePlatformAdapter_dist_eventManager.x), "object" != typeof E.WebAssembly && (E.WebAssembly = WXWebAssembly);
const W = new class extends _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.A {
  constructor() {
    super(...arguments), Object.defineProperty(this, "patchCanvas", { enumerable: true, configurable: true, writable: true, value: _mpChunkDeps__minisheep_threePlatformAdapter_dist_eventManager.M }), Object.defineProperty(this, "patchElement", { enumerable: true, configurable: true, writable: true, value: _mpChunkDeps__minisheep_threePlatformAdapter_dist_eventManager.h });
  }
  getPerformance() {
    return { now: () => Date.now() - y };
  }
  useCanvas(e) {
    return _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.h(this, arguments, void 0, function* (e2, t = _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm.wx$1) {
      if (!this.document) throw new Error("Please use patchGlobal() before useCanvas()");
      const { node: n, width: i, height: o, top: r, right: a, bottom: s, left: u } = yield x(e2, t), { canvas: l, updateRectInfo: c } = this.patchCanvas(n, { width: i, height: o, top: r, right: a, bottom: s, left: u }, this.document);
      function h() {
        return _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.h(this, void 0, void 0, function* () {
          const { width: n2, height: i2, top: o2, right: r2, bottom: a2, left: s2 } = yield x(e2, t);
          c({ width: n2, height: i2, top: o2, right: r2, bottom: a2, left: s2 });
        });
      }
      const f = () => _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.h(this, void 0, void 0, function* () {
        return yield h(), l.getBoundingClientRect();
      });
      return { canvas: l, eventHandler: (e3, t2 = true, n2 = false) => {
        _mpChunkDeps__minisheep_threePlatformAdapter_dist_eventManager.z(e3, [l, t2 && this.document, t2 && this.window], f, !n2);
      }, requestAnimationFrame: n.requestAnimationFrame, cancelAnimationFrame: n.cancelAnimationFrame, useFrame: _mpChunkDeps__minisheep_threePlatformAdapter_dist_eventManager.K(n.requestAnimationFrame, n.cancelAnimationFrame), recomputeSize: h };
    });
  }
  useElement(e) {
    return _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.h(this, arguments, void 0, function* (e2, t = _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm.wx$1) {
      if (!this.document) throw new Error("Please use patchGlobal() before useElement()");
      const { width: n, height: i, top: o, right: r, bottom: a, left: s } = yield x(e2, t), { element: u, updateRectInfo: l } = this.patchElement({}, { width: n, height: i, top: o, right: r, bottom: a, left: s }, this.document);
      function c() {
        return _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.h(this, void 0, void 0, function* () {
          const { width: n2, height: i2, top: o2, right: r2, bottom: a2, left: s2 } = yield x(e2, t);
          l({ width: n2, height: i2, top: o2, right: r2, bottom: a2, left: s2 });
        });
      }
      const h = () => _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.h(this, void 0, void 0, function* () {
        return yield c(), u.getBoundingClientRect();
      });
      return { element: u, eventHandler: (e3, t2 = true, n2 = false) => {
        _mpChunkDeps__minisheep_threePlatformAdapter_dist_eventManager.z(e3, [u, t2 && this.document, t2 && this.window], h, !n2);
      }, recomputeSize: c };
    });
  }
  getWindowInfo() {
    return { get innerWidth() {
      return A.windowInfo.windowWidth;
    }, get innerHeight() {
      return A.windowInfo.windowHeight;
    }, get devicePixelRatio() {
      return A.windowInfo.pixelRatio;
    } };
  }
  get HTMLElement() {
    return _mpChunkDeps__minisheep_threePlatformAdapter_dist_eventManager.d;
  }
  get HTMLCanvasElement() {
    return _mpChunkDeps__minisheep_threePlatformAdapter_dist_eventManager.q;
  }
  get OffscreenCanvas() {
    return _mpChunkDeps__minisheep_threePlatformAdapter_dist_eventManager.$;
  }
  get HTMLImageElement() {
    return _mpChunkDeps__minisheep_threePlatformAdapter_dist_eventManager.V;
  }
  get Image() {
    return _mpChunkDeps__minisheep_threePlatformAdapter_dist_eventManager.X;
  }
  get ImageData() {
    return _mpChunkDeps__minisheep_threePlatformAdapter_dist_eventManager.Y;
  }
  get AudioContext() {
    return _mpChunkDeps__minisheep_threePlatformAdapter_dist_eventManager.W;
  }
  get requestAnimationFrame() {
    return _mpChunkDeps__minisheep_threePlatformAdapter_dist_eventManager.H;
  }
  get cancelAnimationFrame() {
    return _mpChunkDeps__minisheep_threePlatformAdapter_dist_eventManager.G;
  }
}();
_mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.L.useAdapter(W).patch("THREEGlobals");
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/_mpChunkDeps/@minisheep/three-platform-adapter/dist/wechat.js.map
