"use strict";
const _mpChunkDeps__minisheep_threePlatformAdapter_dist_threeOverride_jsm_utils_LocalAsset = require("../utils/LocalAsset.js");
const _mpChunkDeps__minisheep_threePlatformAdapter_dist_threeOverride_jsm_utils_WorkerPool = require("../utils/WorkerPool.js");
const o = function() {
  if ("object" != typeof THREEGlobals["WebAssembly"]) return { supported: false };
  const o2 = _mpChunkDeps__minisheep_threePlatformAdapter_dist_threeOverride_jsm_utils_LocalAsset.LocalAsset.resolve("wasm", "meshopt_decoder.wasm");
  let r;
  const s = THREEGlobals["WebAssembly"].instantiate(o2, {}).then(function(e) {
    r = e.instance, r.exports.__wasm_call_ctors();
  });
  function n(e, t, o3, s2, n2, c2) {
    const d2 = r.exports.sbrk, f2 = o3 + 3 & -4, u2 = d2(f2 * s2), i2 = d2(n2.length), p = new Uint8Array(r.exports.memory.buffer);
    p.set(n2, i2);
    const a = e(u2, o3, s2, i2, n2.length);
    if (0 == a && c2 && c2(u2, f2, s2), t.set(p.subarray(u2, u2 + o3 * s2)), d2(u2 - d2(0)), 0 != a) throw new Error("Malformed buffer data: " + a);
  }
  const c = { NONE: "", OCTAHEDRAL: "meshopt_decodeFilterOct", QUATERNION: "meshopt_decodeFilterQuat", EXPONENTIAL: "meshopt_decodeFilterExp" }, d = { ATTRIBUTES: "meshopt_decodeVertexBuffer", TRIANGLES: "meshopt_decodeIndexBuffer", INDICES: "meshopt_decodeIndexSequence" };
  let f = 0, u = false;
  const i = new _mpChunkDeps__minisheep_threePlatformAdapter_dist_threeOverride_jsm_utils_WorkerPool.WorkerPool();
  return i.setWorkerCreator(() => {
    const t = new THREEGlobals["Worker"](_mpChunkDeps__minisheep_threePlatformAdapter_dist_threeOverride_jsm_utils_LocalAsset.LocalAsset.resolve("worker", "meshopt_decoder.js"));
    return t.postMessage({ type: "init", wasmPath: o2 }), t;
  }), { ready: s, supported: true, dispose() {
    i.dispose();
  }, useWorkers: function(e) {
    u = e > 0, i.setWorkerLimit(e);
  }, decodeVertexBuffer: function(e, t, o3, s2, d2) {
    n(r.exports.meshopt_decodeVertexBuffer, e, t, o3, s2, r.exports[c[d2]]);
  }, decodeIndexBuffer: function(e, t, o3, s2) {
    n(r.exports.meshopt_decodeIndexBuffer, e, t, o3, s2);
  }, decodeIndexSequence: function(e, t, o3, s2) {
    n(r.exports.meshopt_decodeIndexSequence, e, t, o3, s2);
  }, decodeGltfBuffer: function(e, t, o3, s2, f2, u2) {
    n(r.exports[d[f2]], e, t, o3, s2, r.exports[c[u2]]);
  }, decodeGltfBufferAsync: function(e, t, o3, p, a) {
    return u > 0 ? function(e2, t2, o4, r2, s2) {
      return i.postMessage({ type: "decode", data: { id: f++, count: e2, size: t2, source: new Uint8Array(o4), mode: r2, filter: s2 } }).then((e3) => {
        const { action: t3, value: o5 } = e3.data;
        if ("resolve" === t3) throw o5;
        return o5;
      });
    }(e, t, o3, d[p], c[a]) : s.then(function() {
      const s2 = new Uint8Array(e * t);
      return n(r.exports[d[p]], s2, e, t, o3, r.exports[c[a]]), s2;
    });
  } };
}();
exports.o = o;
//# sourceMappingURL=../../../../../../../../.sourcemap/mp-weixin/_mpChunkDeps/@minisheep/three-platform-adapter/dist/three-override/jsm/libs/meshopt_decoder.module.js.map
