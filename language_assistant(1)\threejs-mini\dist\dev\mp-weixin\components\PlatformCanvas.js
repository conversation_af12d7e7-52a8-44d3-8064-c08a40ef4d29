"use strict";
const _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm = require("../common/chunks/@dcloudio/uni-mp-weixin/dist/uni.api.esm.js");
const _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm = require("../common/chunks/@dcloudio/uni-mp-vue/dist/vue.runtime.esm.js");
const _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter = require("../common/chunks/@minisheep/three-platform-adapter/dist/Adapter.js");
const _sfc_main = /* @__PURE__ */ _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm.defineComponent({
  ...{
    options: {
      virtualHost: true
    }
  },
  __name: "PlatformCanvas",
  props: {
    type: {},
    canvasId: {}
  },
  emits: ["touchstart", "touchmove", "touchend", "tap", "touchcancel", "dispatch", "useCanvas"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    let additionHandler = () => {
    };
    const defaultHandler = (e) => {
      if (e.type === "click") {
        e = {
          ...e,
          type: "tap"
        };
      }
      emit(e.type, e);
      emit("dispatch", e);
      additionHandler == null ? void 0 : additionHandler(e);
    };
    _mpChunkDeps__dcloudio_uniMpWeixin_dist_uni_api_esm.index.__f__("log", "at components/PlatformCanvas.vue:72", props.canvasId);
    const instance = _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm.getCurrentInstance();
    _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm.onMounted(() => {
      _mpChunkDeps__minisheep_threePlatformAdapter_dist_Adapter.L.useCanvas(`#${props.canvasId}`, instance.ctx).then((result) => {
        additionHandler = (e) => {
          result.eventHandler(e, false);
        };
        emit("useCanvas", result);
      });
    });
    return (_ctx, _cache) => {
      return {
        a: _ctx.type,
        b: _ctx.canvasId,
        c: _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm.o(defaultHandler),
        d: _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm.o(defaultHandler),
        e: _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm.o(defaultHandler),
        f: _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm.o(defaultHandler),
        g: _mpChunkDeps__dcloudio_uniMpVue_dist_vue_runtime_esm.o(defaultHandler)
      };
    };
  }
});
wx.createComponent(_sfc_main);
//# sourceMappingURL=../../.sourcemap/mp-weixin/components/PlatformCanvas.js.map
